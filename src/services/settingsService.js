import api from './api';

const settingsService = {
  // Get all settings
  getAllSettings: async () => {
    try {
      console.log('Fetching all settings...');
      const response = await api.get('/api/settings');
      console.log('Settings response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Error fetching settings:', error);
      throw error.response ? error.response.data : new Error('Failed to fetch settings');
    }
  },

  // Get setting by key
  getSettingByKey: async (key) => {
    try {
      const response = await api.get(`/api/settings/${key}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching setting ${key}:`, error);
      throw error.response ? error.response.data : new Error('Failed to fetch setting');
    }
  },

  // Update setting
  updateSetting: async (key, value) => {
    try {
      console.log(`Updating setting ${key} with value:`, value);
      const response = await api.put(`/api/settings/${key}`, { value });
      return response.data;
    } catch (error) {
      console.error(`Error updating setting ${key}:`, error);
      throw error.response ? error.response.data : new Error('Failed to update setting');
    }
  },

  // Update multiple settings
  updateMultipleSettings: async (settings) => {
    try {
      console.log('Updating multiple settings:', settings);
      const response = await api.put('/api/settings', { settings });
      return response.data;
    } catch (error) {
      console.error('Error updating multiple settings:', error);
      throw error.response ? error.response.data : new Error('Failed to update settings');
    }
  },

  // Get settings by group
  getSettingsByGroup: async (group) => {
    try {
      const response = await api.get(`/api/settings/group/${group}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching settings for group ${group}:`, error);
      throw error.response ? error.response.data : new Error('Failed to fetch settings by group');
    }
  }
};

export default settingsService;
