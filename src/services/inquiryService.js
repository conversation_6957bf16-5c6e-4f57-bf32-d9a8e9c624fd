import api from './api';

const inquiryService = {
  // Get all inquiries
  getAllInquiries: async () => {
    try {
      const response = await api.get('/inquiries');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch inquiries');
    }
  },

  // Get inquiry by ID
  getInquiryById: async (id) => {
    try {
      const response = await api.get(`/inquiries/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch inquiry');
    }
  },

  // Create new inquiry
  createInquiry: async (inquiryData) => {
    try {
      const response = await api.post('/inquiries', inquiryData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to create inquiry');
    }
  },

  // Update inquiry
  updateInquiry: async (id, inquiryData) => {
    try {
      const response = await api.put(`/inquiries/${id}`, inquiryData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to update inquiry');
    }
  },

  // Delete inquiry
  deleteInquiry: async (id) => {
    try {
      const response = await api.delete(`/inquiries/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to delete inquiry');
    }
  },

  // Get unread inquiries
  getUnreadInquiries: async () => {
    try {
      const response = await api.get('/inquiries/unread');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch unread inquiries');
    }
  },

  // Mark inquiry as read
  markInquiryAsRead: async (id) => {
    try {
      const response = await api.put(`/inquiries/${id}/read`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to mark inquiry as read');
    }
  },

  // Reply to inquiry
  replyToInquiry: async (id, replyData) => {
    try {
      const response = await api.post(`/inquiries/${id}/reply`, replyData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to reply to inquiry');
    }
  }
};

export default inquiryService;
