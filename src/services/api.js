import axios from 'axios';

// Create an axios instance with default config
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:5000/api',
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add a request interceptor to include auth token in requests
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Add a response interceptor to handle common errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('API Error:', error.message);

    // Handle 401 Unauthorized errors (token expired or invalid)
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      console.error('Authentication error:', error.response.data);

      // Only redirect if it's a token expiration issue
      if (error.response.data && (error.response.data.expired ||
          error.response.data.message.includes('expired') ||
          error.response.data.message.includes('token'))) {

        console.log('Session expired, redirecting to login');
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Add a small delay before redirecting to allow the current operation to complete
        setTimeout(() => {
          window.location.href = '/admin/login?expired=true';
        }, 100);
      }
    }

    return Promise.reject(error);
  }
);

export default api;
