import api from './api';

const experienceService = {
  // Get all experiences
  getAllExperiences: async () => {
    try {
      const response = await api.get('/experiences');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch experiences');
    }
  },

  // Get experience by ID
  getExperienceById: async (id) => {
    try {
      const response = await api.get(`/experiences/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch experience');
    }
  },

  // Get experience by slug
  getExperienceBySlug: async (slug) => {
    try {
      const response = await api.get(`/experiences/slug/${slug}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch experience');
    }
  },

  // Get featured experiences
  getFeaturedExperiences: async () => {
    try {
      const response = await api.get('/experiences/featured');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch featured experiences');
    }
  },

  // Create new experience
  createExperience: async (experienceData) => {
    try {
      const response = await api.post('/experiences', experienceData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to create experience');
    }
  },

  // Update experience
  updateExperience: async (id, experienceData) => {
    try {
      const response = await api.put(`/experiences/${id}`, experienceData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to update experience');
    }
  },

  // Delete experience
  deleteExperience: async (id) => {
    try {
      const response = await api.delete(`/experiences/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to delete experience');
    }
  },

  // Get experiences by category
  getExperiencesByCategory: async (category) => {
    try {
      const response = await api.get(`/experiences/category/${category}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch experiences by category');
    }
  }
};

export default experienceService;
