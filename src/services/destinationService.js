import api from './api';

const destinationService = {
  // Get all destinations
  getAllDestinations: async () => {
    try {
      const response = await api.get('/api/destinations');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch destinations');
    }
  },

  // Get destination by ID
  getDestinationById: async (id) => {
    try {
      const response = await api.get(`/api/destinations/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch destination');
    }
  },

  // Get destination by slug
  getDestinationBySlug: async (slug) => {
    try {
      const response = await api.get(`/api/destinations/slug/${slug}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch destination');
    }
  },

  // Get featured destinations
  getFeaturedDestinations: async () => {
    try {
      const response = await api.get('/api/destinations/featured');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch featured destinations');
    }
  },

  // Create new destination
  createDestination: async (destinationData) => {
    try {
      const response = await api.post('/api/destinations', destinationData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to create destination');
    }
  },

  // Update destination
  updateDestination: async (id, destinationData) => {
    try {
      const response = await api.put(`/api/destinations/${id}`, destinationData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to update destination');
    }
  },

  // Delete destination
  deleteDestination: async (id) => {
    try {
      const response = await api.delete(`/api/destinations/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to delete destination');
    }
  },

  // Get destinations by category
  getDestinationsByCategory: async (category) => {
    try {
      const response = await api.get(`/api/destinations/category/${category}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch destinations by category');
    }
  }
};

export default destinationService;
