import api from './api';

const offerService = {
  // Get all offers
  getAllOffers: async () => {
    try {
      const response = await api.get('/offers');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch offers');
    }
  },

  // Get offer by ID
  getOfferById: async (id) => {
    try {
      const response = await api.get(`/offers/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch offer');
    }
  },

  // Get offer by slug
  getOfferBySlug: async (slug) => {
    try {
      const response = await api.get(`/offers/slug/${slug}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch offer');
    }
  },

  // Get active offers
  getActiveOffers: async () => {
    try {
      const response = await api.get('/offers/active');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch active offers');
    }
  },

  // Create new offer
  createOffer: async (offerData) => {
    try {
      const response = await api.post('/offers', offerData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to create offer');
    }
  },

  // Update offer
  updateOffer: async (id, offerData) => {
    try {
      const response = await api.put(`/offers/${id}`, offerData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to update offer');
    }
  },

  // Delete offer
  deleteOffer: async (id) => {
    try {
      const response = await api.delete(`/offers/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to delete offer');
    }
  },

  // Get offers by destination
  getOffersByDestination: async (destinationId) => {
    try {
      const response = await api.get(`/offers/destination/${destinationId}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch offers by destination');
    }
  }
};

export default offerService;
