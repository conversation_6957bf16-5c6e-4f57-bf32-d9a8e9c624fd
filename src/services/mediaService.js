import api from './api';
import axios from 'axios';

const mediaService = {
  // Get all media items
  getAllMedia: async () => {
    try {
      const response = await api.get('/media');
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch media items');
    }
  },

  // Get media by ID
  getMediaById: async (id) => {
    try {
      const response = await api.get(`/media/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch media item');
    }
  },

  // Upload media
  uploadMedia: async (formData) => {
    try {
      // For file uploads, we need to use the full URL to avoid baseURL issues with multipart/form-data
      const token = localStorage.getItem('token');

      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      console.log('Uploading media with token:', token.substring(0, 20) + '...');

      // Use direct axios call with full URL
      const response = await axios.post('http://localhost:5000/api/media/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'Authorization': `Bearer ${token}`
        },
      });
      console.log('Upload response:', response.data);
      return response.data;
    } catch (error) {
      console.error('Upload error:', error);

      // Handle token expiration
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        console.error('Authentication error:', error.response.data);

        // Clear token and redirect to login
        localStorage.removeItem('token');
        localStorage.removeItem('user');

        // Redirect to login page
        window.location.href = '/admin/login';

        throw new Error('Your session has expired. Please log in again.');
      }

      if (error.response) {
        console.error('Error response:', error.response.data);
        console.error('Error status:', error.response.status);
      }

      throw error.response ? error.response.data : new Error('Failed to upload media');
    }
  },

  // Update media metadata
  updateMedia: async (id, mediaData) => {
    try {
      const response = await api.put(`/media/${id}`, mediaData);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to update media');
    }
  },

  // Delete media
  deleteMedia: async (id) => {
    try {
      const response = await api.delete(`/media/${id}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to delete media');
    }
  },

  // Get media by type
  getMediaByType: async (type) => {
    try {
      const response = await api.get(`/media/type/${type}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to fetch media by type');
    }
  },

  // Search media
  searchMedia: async (query) => {
    try {
      const response = await api.get(`/media/search?q=${query}`);
      return response.data;
    } catch (error) {
      throw error.response ? error.response.data : new Error('Failed to search media');
    }
  }
};

export default mediaService;
