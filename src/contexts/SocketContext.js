import React, { createContext, useContext, useEffect, useState } from 'react';
import { io } from 'socket.io-client';
import { useAuth } from '../context/AuthContext';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [notifications, setNotifications] = useState([]);
  const { user } = useAuth();

  useEffect(() => {
    // Only connect if user is authenticated
    if (user) {
      console.log('Connecting to Socket.IO server...');
      const newSocket = io('http://localhost:5000', {
        transports: ['websocket', 'polling']
      });

      newSocket.on('connect', () => {
        console.log('Connected to Socket.IO server');
        setConnected(true);

        // Join admin room if user is admin or editor
        if (user.role === 'admin' || user.role === 'editor') {
          newSocket.emit('join-admin');
        }
      });

      newSocket.on('disconnect', () => {
        console.log('Disconnected from Socket.IO server');
        setConnected(false);
      });

      // Listen for real-time updates
      newSocket.on('user-created', (data) => {
        console.log('User created:', data);
        addNotification('success', `New user "${data.name}" has been created`);
      });

      newSocket.on('user-updated', (data) => {
        console.log('User updated:', data);
        addNotification('info', `User "${data.name}" has been updated`);
      });

      newSocket.on('user-deleted', (data) => {
        console.log('User deleted:', data);
        addNotification('warning', `User has been deleted`);
      });

      newSocket.on('destination-created', (data) => {
        console.log('Destination created:', data);
        addNotification('success', `New destination "${data.title}" has been created`);
      });

      newSocket.on('destination-updated', (data) => {
        console.log('Destination updated:', data);
        addNotification('info', `Destination "${data.title}" has been updated`);
      });

      newSocket.on('destination-deleted', (data) => {
        console.log('Destination deleted:', data);
        addNotification('warning', `Destination has been deleted`);
      });

      newSocket.on('experience-created', (data) => {
        console.log('Experience created:', data);
        addNotification('success', `New experience "${data.title}" has been created`);
      });

      newSocket.on('experience-updated', (data) => {
        console.log('Experience updated:', data);
        addNotification('info', `Experience "${data.title}" has been updated`);
      });

      newSocket.on('experience-deleted', (data) => {
        console.log('Experience deleted:', data);
        addNotification('warning', `Experience has been deleted`);
      });

      newSocket.on('offer-created', (data) => {
        console.log('Offer created:', data);
        addNotification('success', `New offer "${data.title}" has been created`);
      });

      newSocket.on('offer-updated', (data) => {
        console.log('Offer updated:', data);
        addNotification('info', `Offer "${data.title}" has been updated`);
      });

      newSocket.on('offer-deleted', (data) => {
        console.log('Offer deleted:', data);
        addNotification('warning', `Offer has been deleted`);
      });

      newSocket.on('inquiry-created', (data) => {
        console.log('Inquiry created:', data);
        addNotification('info', `New inquiry from "${data.name}"`);
      });

      newSocket.on('inquiry-updated', (data) => {
        console.log('Inquiry updated:', data);
        addNotification('info', `Inquiry from "${data.name}" has been updated`);
      });

      newSocket.on('inquiry-replied', (data) => {
        console.log('Inquiry replied:', data);
        addNotification('success', `Reply sent to "${data.name}"`);
      });

      newSocket.on('inquiry-deleted', (data) => {
        console.log('Inquiry deleted:', data);
        addNotification('warning', `Inquiry has been deleted`);
      });

      newSocket.on('media-uploaded', (data) => {
        console.log('Media uploaded:', data);
        addNotification('success', `New media "${data.title}" has been uploaded`);
      });

      newSocket.on('media-updated', (data) => {
        console.log('Media updated:', data);
        addNotification('info', `Media "${data.title}" has been updated`);
      });

      newSocket.on('media-deleted', (data) => {
        console.log('Media deleted:', data);
        addNotification('warning', `Media has been deleted`);
      });

      newSocket.on('setting-updated', (data) => {
        console.log('Setting updated:', data);
        addNotification('info', `Setting "${data.key_name}" has been updated`);
      });

      setSocket(newSocket);

      return () => {
        console.log('Cleaning up Socket.IO connection');
        newSocket.close();
      };
    } else {
      // Disconnect if user logs out
      if (socket) {
        socket.close();
        setSocket(null);
        setConnected(false);
      }
    }
  }, [user]);

  const addNotification = (type, message) => {
    const notification = {
      id: Date.now(),
      type,
      message,
      timestamp: new Date()
    };

    setNotifications(prev => [notification, ...prev.slice(0, 9)]); // Keep only 10 notifications

    // Auto-remove notification after 5 seconds
    setTimeout(() => {
      removeNotification(notification.id);
    }, 5000);
  };

  const removeNotification = (id) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  const clearAllNotifications = () => {
    setNotifications([]);
  };

  const value = {
    socket,
    connected,
    notifications,
    addNotification,
    removeNotification,
    clearAllNotifications
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
