import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

const PageBanner = styled.section`
  height: 60vh;
  min-height: 400px;
  background-image: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('https://images.unsplash.com/photo-1602002418816-5c0aeef426aa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
`;

const BannerContent = styled.div`
  max-width: 800px;
  padding: 0 20px;

  h1 {
    font-size: 56px;
    font-weight: 700;
    margin-bottom: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);

    @media (max-width: 768px) {
      font-size: 40px;
    }
  }

  p {
    font-size: 20px;
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.6;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);

    @media (max-width: 768px) {
      font-size: 18px;
    }
  }
`;

const SectionContainer = styled.section`
  padding: 80px 0;

  &:nth-child(even) {
    background-color: #f8f9fa;
  }
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: 60px;

  h2 {
    font-size: 42px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #333;
    position: relative;

    &:after {
      content: '';
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      width: 80px;
      height: 4px;
      background: linear-gradient(90deg, #0099b8, #00c4cc);
      border-radius: 2px;
    }

    @media (max-width: 768px) {
      font-size: 32px;
    }
  }

  p {
    font-size: 18px;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
    line-height: 1.7;
  }
`;

const TwoColumnGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  align-items: center;

  @media (max-width: 968px) {
    grid-template-columns: 1fr;
    gap: 40px;
  }
`;

const ContentBlock = styled.div`
  h3 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
  }

  p {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
    margin-bottom: 20px;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 20px 0;

    li {
      font-size: 16px;
      color: #555;
      margin-bottom: 12px;
      padding-left: 30px;
      position: relative;

      &:before {
        content: '✓';
        position: absolute;
        left: 0;
        color: #0099b8;
        font-weight: bold;
        font-size: 18px;
      }
    }
  }
`;

const ImageContainer = styled.div`
  img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 60px;
`;

const StatItem = styled.div`
  text-align: center;
  padding: 30px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }

  .number {
    font-size: 36px;
    font-weight: 700;
    color: #0099b8;
    margin-bottom: 10px;
  }

  .label {
    font-size: 16px;
    color: #666;
    font-weight: 500;
  }
`;

const VisionMissionGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 40px;
  }
`;

const VisionMissionCard = styled.div`
  background: white;
  padding: 40px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  text-align: center;
  position: relative;
  overflow: hidden;

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 6px;
    background: ${props => props.type === 'vision' ? 'linear-gradient(90deg, #0099b8, #00c4cc)' : 'linear-gradient(90deg, #ff6b6b, #ffa726)'};
  }

  .icon {
    font-size: 48px;
    margin-bottom: 20px;
  }

  h3 {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 20px;
    color: #333;
  }

  p {
    font-size: 16px;
    line-height: 1.8;
    color: #555;
  }
`;

const ValuesContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 30px;
  margin-top: 40px;
`;

const ValueCard = styled.div`
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
  text-align: center;
  transition: transform 0.3s ease;

  &:hover {
    transform: translateY(-5px);
  }

  .icon {
    font-size: 40px;
    margin-bottom: 15px;
  }

  h4 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  p {
    font-size: 14px;
    color: #666;
    line-height: 1.6;
  }
`;

const AboutPage = () => {
  const [content, setContent] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchContent = async () => {
      try {
        const response = await fetch('/api/page-content/about-us');
        if (response.ok) {
          const data = await response.json();
          setContent(data.content);
        } else {
          console.error('Failed to fetch about content');
        }
      } catch (error) {
        console.error('Error fetching about content:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchContent();
  }, []);

  if (loading) {
    return (
      <div style={{ padding: '100px 20px', textAlign: 'center' }}>
        <h2>Loading...</h2>
      </div>
    );
  }

  if (!content) {
    return (
      <div style={{ padding: '100px 20px', textAlign: 'center' }}>
        <h2>Content not available</h2>
      </div>
    );
  }

  return (
    <>
      <PageBanner>
        <BannerContent>
          <h1>{content.hero?.title || 'About Lux Voyage'}</h1>
          <p>{content.hero?.subtitle || 'Discover our story, vision, and commitment to creating extraordinary travel experiences in the Maldives'}</p>
        </BannerContent>
      </PageBanner>

      {/* Company Profile Section */}
      <SectionContainer>
        <Container>
          <SectionHeader>
            <h2>{content.companyProfile?.title || 'Our Story'}</h2>
            <p>{content.companyProfile?.subtitle || 'Founded with a passion for luxury travel and the pristine beauty of the Maldives'}</p>
          </SectionHeader>

          <TwoColumnGrid>
            <ContentBlock>
              <h3>{content.companyProfile?.mainTitle || 'Welcome to Lux Voyage Travel Agency'}</h3>
              <p>
                {content.companyProfile?.description || 'Established as a premier travel agency specializing in luxury Maldivian experiences, Lux Voyage has been crafting unforgettable journeys to paradise for discerning travelers worldwide.'}
              </p>
              <p>
                {content.companyProfile?.secondDescription || 'From the moment you begin planning your journey with us, our dedicated team works tirelessly to ensure every detail of your Maldivian adventure exceeds your expectations.'}
              </p>
              <ul>
                {content.companyProfile?.features?.map((feature, index) => (
                  <li key={index}>{feature}</li>
                )) || (
                  <>
                    <li>Personalized luxury travel experiences</li>
                    <li>Expert knowledge of Maldivian destinations</li>
                    <li>24/7 customer support and assistance</li>
                    <li>Exclusive partnerships with premium resorts</li>
                    <li>Sustainable and responsible tourism practices</li>
                  </>
                )}
              </ul>
            </ContentBlock>

            <ImageContainer>
              <img
                src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1170&q=80"
                alt="Luxury Maldives Resort"
              />
            </ImageContainer>
          </TwoColumnGrid>

          <StatsContainer>
            {content.companyProfile?.stats?.map((stat, index) => (
              <StatItem key={index}>
                <div className="number">{stat.number}</div>
                <div className="label">{stat.label}</div>
              </StatItem>
            )) || (
              <>
                <StatItem>
                  <div className="number">500+</div>
                  <div className="label">Happy Travelers</div>
                </StatItem>
                <StatItem>
                  <div className="number">50+</div>
                  <div className="label">Partner Resorts</div>
                </StatItem>
                <StatItem>
                  <div className="number">15+</div>
                  <div className="label">Years Experience</div>
                </StatItem>
                <StatItem>
                  <div className="number">98%</div>
                  <div className="label">Customer Satisfaction</div>
                </StatItem>
              </>
            )}
          </StatsContainer>
        </Container>
      </SectionContainer>

      {/* Vision & Mission Section */}
      <SectionContainer>
        <Container>
          <SectionHeader>
            <h2>Our Vision & Mission</h2>
            <p>Guiding principles that drive our commitment to exceptional travel experiences</p>
          </SectionHeader>

          <VisionMissionGrid>
            <VisionMissionCard type="vision">
              <div className="icon">🌟</div>
              <h3>{content.vision?.title || 'Our Vision'}</h3>
              <p>
                {content.vision?.content || 'To be the world\'s leading luxury travel agency for Maldivian experiences, recognized for our unparalleled service, deep local expertise, and commitment to creating transformative journeys that connect travelers with the natural beauty and cultural richness of the Maldives.'}
              </p>
            </VisionMissionCard>

            <VisionMissionCard type="mission">
              <div className="icon">🎯</div>
              <h3>{content.mission?.title || 'Our Mission'}</h3>
              <p>
                {content.mission?.content || 'To curate and deliver exceptional, personalized luxury travel experiences in the Maldives that exceed our clients\' expectations while promoting sustainable tourism practices that preserve the natural beauty and cultural heritage of these pristine islands for future generations.'}
              </p>
            </VisionMissionCard>
          </VisionMissionGrid>
        </Container>
      </SectionContainer>

      {/* Company Values Section */}
      <SectionContainer>
        <Container>
          <SectionHeader>
            <h2>Our Core Values</h2>
            <p>The principles that guide everything we do at Lux Voyage</p>
          </SectionHeader>

          <ValuesContainer>
            {content.values?.map((value, index) => (
              <ValueCard key={index}>
                <div className="icon">{value.icon}</div>
                <h4>{value.title}</h4>
                <p>{value.description}</p>
              </ValueCard>
            )) || (
              <>
                <ValueCard>
                  <div className="icon">🏆</div>
                  <h4>Excellence</h4>
                  <p>We strive for perfection in every aspect of our service, from initial consultation to your safe return home.</p>
                </ValueCard>
                <ValueCard>
                  <div className="icon">🤝</div>
                  <h4>Trust</h4>
                  <p>Building lasting relationships through transparency, reliability, and honest communication with our clients.</p>
                </ValueCard>
                <ValueCard>
                  <div className="icon">🌱</div>
                  <h4>Sustainability</h4>
                  <p>Committed to responsible tourism that protects the Maldives' pristine environment for future generations.</p>
                </ValueCard>
                <ValueCard>
                  <div className="icon">💎</div>
                  <h4>Luxury</h4>
                  <p>Curating premium experiences that offer the finest accommodations, services, and exclusive access.</p>
                </ValueCard>
                <ValueCard>
                  <div className="icon">🎨</div>
                  <h4>Personalization</h4>
                  <p>Every journey is uniquely crafted to match your individual preferences, interests, and travel style.</p>
                </ValueCard>
                <ValueCard>
                  <div className="icon">🌍</div>
                  <h4>Cultural Respect</h4>
                  <p>Honoring and celebrating the rich Maldivian culture while creating authentic, respectful experiences.</p>
                </ValueCard>
              </>
            )}
          </ValuesContainer>
        </Container>
      </SectionContainer>

      {/* Why Choose Us Section */}
      <SectionContainer>
        <Container>
          <TwoColumnGrid>
            <ImageContainer>
              <img
                src="https://images.unsplash.com/photo-1573843981267-be1999ff37cd?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1074&q=80"
                alt="Maldives Crystal Clear Waters"
              />
            </ImageContainer>

            <ContentBlock>
              <h3>{content.whyChooseUs?.title || 'Why Choose Lux Voyage?'}</h3>
              <p>
                {content.whyChooseUs?.description || 'With countless travel agencies offering Maldivian experiences, what sets Lux Voyage apart is our unwavering commitment to excellence and our deep-rooted passion for these magnificent islands.'}
              </p>
              <p>
                {content.whyChooseUs?.secondDescription || 'Our team consists of travel specialists who have personally explored every resort, experienced every activity, and built relationships with local partners across the Maldives. This firsthand knowledge allows us to provide insider insights and recommendations that you simply won\'t find elsewhere.'}
              </p>
              <ul>
                {content.whyChooseUs?.features?.map((feature, index) => (
                  <li key={index}>{feature}</li>
                )) || (
                  <>
                    <li>Exclusive access to private resorts and experiences</li>
                    <li>Competitive pricing through direct partnerships</li>
                    <li>Comprehensive travel insurance and protection</li>
                    <li>Multi-language customer support</li>
                    <li>Emergency assistance and 24/7 concierge services</li>
                    <li>Flexible booking and cancellation policies</li>
                  </>
                )}
              </ul>
            </ContentBlock>
          </TwoColumnGrid>
        </Container>
      </SectionContainer>
    </>
  );
};

export default AboutPage;
