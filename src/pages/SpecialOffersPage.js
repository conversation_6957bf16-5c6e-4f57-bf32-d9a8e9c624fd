import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import OfferCard from '../components/cards/OfferCard';
import CardGrid from '../components/cards/CardGrid';
import EmptyState from '../components/common/EmptyState';

const PageBanner = styled.section`
  height: 50vh;
  min-height: 300px;
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1514282401047-d79a71a590e8?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
`;

const BannerContent = styled.div`
  max-width: 800px;
  padding: 0 20px;

  h1 {
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      font-size: 36px;
    }
  }

  p {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }
`;

const SectionContainer = styled.section`
  padding: 80px 0;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: 40px;

  h2 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  p {
    font-size: 18px;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
  }
`;

const SpecialOffersPage = () => {
  const [offers, setOffers] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch offers from API
    const fetchOffers = async () => {
      try {
        const response = await fetch('/api/offers');
        if (response.ok) {
          const data = await response.json();
          setOffers(data.map(offer => ({
            ...offer,
            imageUrl: offer.image_url,
            validUntil: offer.valid_to
          })));
        } else {
          console.error('Failed to fetch offers');
          setOffers([]);
        }
      } catch (error) {
        console.error('Error fetching offers:', error);
        setOffers([]);
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, []);

  return (
    <>
      <PageBanner>
        <BannerContent>
          <h1>Special Offers</h1>
          <p>Exclusive packages and limited-time deals for your dream vacation</p>
        </BannerContent>
      </PageBanner>

      <SectionContainer>
        <Container>
          <SectionHeader>
            <h2>Limited Time Deals</h2>
            <p>Take advantage of our exclusive offers and save on your next luxury vacation in the Maldives</p>
          </SectionHeader>

          {loading ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>Loading offers...</div>
          ) : offers.length > 0 ? (
            <CardGrid>
              {offers.map(offer => (
                <OfferCard key={offer.id} offer={offer} />
              ))}
            </CardGrid>
          ) : (
            <EmptyState type="offers" />
          )}
        </Container>
      </SectionContainer>
    </>
  );
};

export default SpecialOffersPage;
