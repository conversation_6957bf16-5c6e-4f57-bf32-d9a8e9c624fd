import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import DestinationCard from '../components/cards/DestinationCard';
import ExperienceCard from '../components/cards/ExperienceCard';
import OfferCard from '../components/cards/OfferCard';
import EmptyState from '../components/common/EmptyState';
import CardGrid from '../components/cards/CardGrid';

const HeroSection = styled.section`
  height: 100vh;
  min-height: 600px;
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1573843981267-be1999ff37cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
`;

const HeroContent = styled.div`
  max-width: 800px;
  padding: 0 20px;

  h1 {
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      font-size: 36px;
    }
  }

  p {
    font-size: 18px;
    margin-bottom: 30px;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }
`;

const Button = styled(Link)`
  display: inline-block;
  background-color: #0099b8;
  color: #fff;
  padding: 12px 24px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;

  &:hover {
    background-color: #007a94;
  }
`;

const SectionContainer = styled.section`
  padding: 80px 0;
  background-color: ${props => props.bgColor || '#fff'};
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: 40px;

  span {
    display: inline-block;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 2px;
    color: #0099b8;
    margin-bottom: 10px;
  }

  h2 {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin: 0 0 15px;

    @media (max-width: 768px) {
      font-size: 28px;
    }
  }

  p {
    font-size: 16px;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
  }
`;

const HomePage = () => {
  const [destinations, setDestinations] = useState([]);
  const [experiences, setExperiences] = useState([]);
  const [offers, setOffers] = useState([]);
  const [loading, setLoading] = useState({
    destinations: true,
    experiences: true,
    offers: true
  });

  useEffect(() => {
    // Fetch featured destinations from API
    const fetchDestinations = async () => {
      try {
        const response = await fetch('/api/destinations/featured');
        if (response.ok) {
          const data = await response.json();
          setDestinations(data.map(dest => ({
            ...dest,
            imageUrl: dest.image_url
          })));
        } else {
          console.error('Failed to fetch destinations');
          setDestinations([]);
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
        setDestinations([]);
      } finally {
        setLoading(prev => ({ ...prev, destinations: false }));
      }
    };

    fetchDestinations();

    // Fetch featured experiences from API
    const fetchExperiences = async () => {
      try {
        const response = await fetch('/api/experiences/featured');
        if (response.ok) {
          const data = await response.json();
          setExperiences(data.map(exp => ({
            ...exp,
            imageUrl: exp.image_url
          })));
        } else {
          console.error('Failed to fetch experiences');
          setExperiences([]);
        }
      } catch (error) {
        console.error('Error fetching experiences:', error);
        setExperiences([]);
      } finally {
        setLoading(prev => ({ ...prev, experiences: false }));
      }
    };

    fetchExperiences();

    // Fetch featured offers from API
    const fetchOffers = async () => {
      try {
        const response = await fetch('/api/offers/featured');
        if (response.ok) {
          const data = await response.json();
          setOffers(data.map(offer => ({
            ...offer,
            imageUrl: offer.image_url,
            validUntil: offer.valid_to
          })));
        } else {
          console.error('Failed to fetch offers');
          setOffers([]);
        }
      } catch (error) {
        console.error('Error fetching offers:', error);
        setOffers([]);
      } finally {
        setLoading(prev => ({ ...prev, offers: false }));
      }
    };

    fetchOffers();
  }, []);

  return (
    <>
      <HeroSection>
        <HeroContent>
          <h1>Experience Paradise in the Maldives</h1>
          <p>Discover pristine beaches, crystal-clear waters, and luxurious resorts</p>
          <Button to="/destinations">Explore Now</Button>
        </HeroContent>
      </HeroSection>

      <SectionContainer bgColor="#f9f9f9">
        <Container>
          <SectionHeader>
            <span>Explore</span>
            <h2>Our Destinations</h2>
            <p>Discover the most beautiful islands and resorts in the Maldives</p>
          </SectionHeader>

          {loading.destinations ? (
            <div style={{ textAlign: 'center', padding: '30px 0' }}>Loading destinations...</div>
          ) : destinations.length > 0 ? (
            <CardGrid>
              {destinations.map(destination => (
                <DestinationCard key={destination.id} destination={destination} />
              ))}
            </CardGrid>
          ) : (
            <EmptyState type="destinations" />
          )}

          <div style={{ textAlign: 'center', marginTop: '40px' }}>
            <Button to="/destinations">View All Destinations</Button>
          </div>
        </Container>
      </SectionContainer>

      <SectionContainer>
        <Container>
          <SectionHeader>
            <span>Activities</span>
            <h2>Unforgettable Experiences</h2>
            <p>Create memories that will last a lifetime</p>
          </SectionHeader>

          {loading.experiences ? (
            <div style={{ textAlign: 'center', padding: '30px 0' }}>Loading experiences...</div>
          ) : experiences.length > 0 ? (
            <CardGrid>
              {experiences.map(experience => (
                <ExperienceCard key={experience.id} experience={experience} />
              ))}
            </CardGrid>
          ) : (
            <EmptyState type="experiences" />
          )}

          <div style={{ textAlign: 'center', marginTop: '40px' }}>
            <Button to="/experiences">View All Experiences</Button>
          </div>
        </Container>
      </SectionContainer>

      <SectionContainer bgColor="#f9f9f9">
        <Container>
          <SectionHeader>
            <span>Deals</span>
            <h2>Special Offers</h2>
            <p>Exclusive packages and limited-time deals for your dream vacation</p>
          </SectionHeader>

          {loading.offers ? (
            <div style={{ textAlign: 'center', padding: '30px 0' }}>Loading offers...</div>
          ) : offers.length > 0 ? (
            <CardGrid>
              {offers.map(offer => (
                <OfferCard key={offer.id} offer={offer} />
              ))}
            </CardGrid>
          ) : (
            <EmptyState type="offers" />
          )}

          <div style={{ textAlign: 'center', marginTop: '40px' }}>
            <Button to="/special-offers">View All Offers</Button>
          </div>
        </Container>
      </SectionContainer>
    </>
  );
};

export default HomePage;
