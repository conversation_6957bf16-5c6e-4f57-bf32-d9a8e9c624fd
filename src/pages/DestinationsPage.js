import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import DestinationCard from '../components/cards/DestinationCard';
import CardGrid from '../components/cards/CardGrid';
import EmptyState from '../components/common/EmptyState';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHotel, faUmbrellaBeach, faBuilding, faHome } from '@fortawesome/free-solid-svg-icons';

const PageBanner = styled.section`
  height: 50vh;
  min-height: 300px;
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1573843981267-be1999ff37cd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
`;

const BannerContent = styled.div`
  max-width: 800px;
  padding: 0 20px;

  h1 {
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      font-size: 36px;
    }
  }

  p {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }
`;

const SectionContainer = styled.section`
  padding: 80px 0;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: 40px;

  h2 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  p {
    font-size: 18px;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
  }
`;

const CategoryFilter = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 10px;
`;

const CategoryButton = styled.button`
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background-color: ${props => props.active ? '#0099b8' : '#f5f5f5'};
  color: ${props => props.active ? 'white' : '#333'};
  border: none;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: ${props => props.active ? '#007a94' : '#e0e0e0'};
  }

  svg {
    margin-right: 8px;
  }

  @media (max-width: 768px) {
    font-size: 14px;
    padding: 8px 16px;
  }
`;

const CategoryBadge = styled.span`
  display: inline-block;
  padding: 4px 10px;
  background-color: #0099b8;
  color: white;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  margin-top: 10px;
`;

const DestinationsPage = () => {
  const [destinations, setDestinations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Destinations', icon: null },
    { id: 'resort', name: 'Resorts', icon: faHotel },
    { id: 'private-island', name: 'Private Islands', icon: faUmbrellaBeach },
    { id: 'hotel', name: 'Hotels', icon: faBuilding },
    { id: 'guesthouse', name: 'Guesthouses', icon: faHome }
  ];

  useEffect(() => {
    // Fetch destinations from API
    const fetchDestinations = async () => {
      try {
        const response = await fetch('/api/destinations');
        if (response.ok) {
          const data = await response.json();
          setDestinations(data.map(dest => ({
            ...dest,
            imageUrl: dest.image_url
          })));
        } else {
          console.error('Failed to fetch destinations');
          setDestinations([]);
        }
      } catch (error) {
        console.error('Error fetching destinations:', error);
        setDestinations([]);
      } finally {
        setLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  const handleCategoryChange = (categoryId) => {
    setActiveCategory(categoryId);
  };

  const filteredDestinations = activeCategory === 'all'
    ? destinations
    : destinations.filter(destination => destination.category === activeCategory);

  return (
    <>
      <PageBanner>
        <BannerContent>
          <h1>Our Destinations</h1>
          <p>Discover the most beautiful islands and resorts in the Maldives</p>
        </BannerContent>
      </PageBanner>

      <SectionContainer>
        <Container>
          <SectionHeader>
            <h2>Explore Paradise</h2>
            <p>From luxurious resorts to authentic local islands, find your perfect getaway in the Maldives</p>
          </SectionHeader>

          {loading ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>Loading destinations...</div>
          ) : destinations.length > 0 ? (
            <>
              <CategoryFilter>
                {categories.map(category => (
                  <CategoryButton
                    key={category.id}
                    active={activeCategory === category.id}
                    onClick={() => handleCategoryChange(category.id)}
                  >
                    {category.icon && <FontAwesomeIcon icon={category.icon} />}
                    {category.name}
                  </CategoryButton>
                ))}
              </CategoryFilter>

              {filteredDestinations.length > 0 ? (
                <CardGrid>
                  {filteredDestinations.map(destination => (
                    <DestinationCard
                      key={destination.id}
                      destination={{
                        ...destination,
                        categoryLabel: categories.find(cat => cat.id === destination.category)?.name
                      }}
                    />
                  ))}
                </CardGrid>
              ) : (
                <div style={{ textAlign: 'center', padding: '50px 0' }}>
                  <p>No destinations found in this category.</p>
                </div>
              )}
            </>
          ) : (
            <EmptyState type="destinations" />
          )}
        </Container>
      </SectionContainer>
    </>
  );
};

export default DestinationsPage;
