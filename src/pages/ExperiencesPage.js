import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import ExperienceCard from '../components/cards/ExperienceCard';
import CardGrid from '../components/cards/CardGrid';
import EmptyState from '../components/common/EmptyState';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faWater, faSpa, faUmbrellaBeach, faUtensils, faShip, faFish } from '@fortawesome/free-solid-svg-icons';

const PageBanner = styled.section`
  height: 50vh;
  min-height: 300px;
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)), url('https://images.unsplash.com/photo-1540541338287-41700207dee6?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1920&q=80');
  background-size: cover;
  background-position: center;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: #fff;
`;

const BannerContent = styled.div`
  max-width: 800px;
  padding: 0 20px;

  h1 {
    font-size: 48px;
    font-weight: 600;
    margin-bottom: 20px;

    @media (max-width: 768px) {
      font-size: 36px;
    }
  }

  p {
    font-size: 18px;
    max-width: 600px;
    margin: 0 auto;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }
`;

const SectionContainer = styled.section`
  padding: 80px 0;
`;

const Container = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
`;

const SectionHeader = styled.div`
  text-align: center;
  margin-bottom: 40px;

  h2 {
    font-size: 36px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
  }

  p {
    font-size: 18px;
    color: #666;
    max-width: 700px;
    margin: 0 auto;
  }
`;

const CategoryFilter = styled.div`
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
  gap: 10px;
`;

const CategoryButton = styled.button`
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background-color: ${props => props.active ? '#0099b8' : '#f5f5f5'};
  color: ${props => props.active ? 'white' : '#333'};
  border: none;
  border-radius: 30px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

  &:hover {
    background-color: ${props => props.active ? '#007a94' : '#e0e0e0'};
  }

  svg {
    margin-right: 8px;
  }

  @media (max-width: 768px) {
    font-size: 14px;
    padding: 8px 16px;
  }
`;

const ExperiencesPage = () => {
  const [experiences, setExperiences] = useState([]);
  const [loading, setLoading] = useState(true);
  const [activeCategory, setActiveCategory] = useState('all');

  const categories = [
    { id: 'all', name: 'All Activities', icon: null },
    { id: 'water-activities', name: 'Water Activities', icon: faWater },
    { id: 'wellness', name: 'Wellness', icon: faSpa },
    { id: 'excursions', name: 'Excursions', icon: faShip },
    { id: 'culinary', name: 'Culinary', icon: faUtensils },
    { id: 'beach', name: 'Beach Activities', icon: faUmbrellaBeach },
    { id: 'fishing', name: 'Fishing', icon: faFish }
  ];

  useEffect(() => {
    // Fetch experiences from API
    const fetchExperiences = async () => {
      try {
        const response = await fetch('/api/experiences');
        if (response.ok) {
          const data = await response.json();
          setExperiences(data.map(exp => ({
            ...exp,
            imageUrl: exp.image_url
          })));
        } else {
          console.error('Failed to fetch experiences');
          setExperiences([]);
        }
      } catch (error) {
        console.error('Error fetching experiences:', error);
        setExperiences([]);
      } finally {
        setLoading(false);
      }
    };

    fetchExperiences();
  }, []);

  const handleCategoryChange = (categoryId) => {
    setActiveCategory(categoryId);
  };

  const filteredExperiences = activeCategory === 'all'
    ? experiences
    : experiences.filter(experience => experience.category === activeCategory);

  return (
    <>
      <PageBanner>
        <BannerContent>
          <h1>Unforgettable Experiences</h1>
          <p>Create memories that will last a lifetime</p>
        </BannerContent>
      </PageBanner>

      <SectionContainer>
        <Container>
          <SectionHeader>
            <h2>Discover Activities</h2>
            <p>From underwater adventures to relaxing spa treatments, find the perfect experiences for your Maldives vacation</p>
          </SectionHeader>

          {loading ? (
            <div style={{ textAlign: 'center', padding: '50px 0' }}>Loading experiences...</div>
          ) : experiences.length > 0 ? (
            <>
              <CategoryFilter>
                {categories.map(category => (
                  <CategoryButton
                    key={category.id}
                    active={activeCategory === category.id}
                    onClick={() => handleCategoryChange(category.id)}
                  >
                    {category.icon && <FontAwesomeIcon icon={category.icon} />}
                    {category.name}
                  </CategoryButton>
                ))}
              </CategoryFilter>

              {filteredExperiences.length > 0 ? (
                <CardGrid>
                  {filteredExperiences.map(experience => (
                    <ExperienceCard
                      key={experience.id}
                      experience={{
                        ...experience,
                        categoryLabel: categories.find(cat => cat.id === experience.category)?.name
                      }}
                    />
                  ))}
                </CardGrid>
              ) : (
                <div style={{ textAlign: 'center', padding: '50px 0' }}>
                  <p>No experiences found in this category.</p>
                </div>
              )}
            </>
          ) : (
            <EmptyState type="experiences" />
          )}
        </Container>
      </SectionContainer>
    </>
  );
};

export default ExperiencesPage;
