import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';

import PageHeader from '../../components/dashboard/PageHeader';
import DataTable from '../../components/dashboard/DataTable';
import Modal from '../../components/dashboard/Modal';
import FormBuilder from '../../components/dashboard/FormBuilder';
import { offerService, mediaService } from '../../services';

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${({ type }) => {
    switch (type) {
      case 'success':
        return '#e8f5e9';
      case 'error':
        return '#ffebee';
      default:
        return '#e3f2fd';
    }
  }};
  color: ${({ type }) => {
    switch (type) {
      case 'success':
        return '#388e3c';
      case 'error':
        return '#d32f2f';
      default:
        return '#1976d2';
    }
  }};
`;

const ImagePreview = styled.img`
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
`;

const OffersPage = () => {
  // State for offers data
  const [offers, setOffers] = useState([]);
  const [loading, setLoading] = useState(true);

  // State for modals
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // State for form data
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});
  const [selectedOffer, setSelectedOffer] = useState(null);

  // Fetch offers from API
  useEffect(() => {
    const fetchOffers = async () => {
      try {
        setLoading(true);
        const data = await offerService.getAllOffers();

        // Map API data to component format
        const mappedOffers = data.map(offer => ({
          ...offer,
          active: new Date(offer.validTo) >= new Date(), // Set active based on validity
          validUntil: offer.validTo // Map validTo to validUntil for compatibility
        }));

        setOffers(mappedOffers);
      } catch (error) {
        console.error('Error fetching offers:', error);
        // Show error message to user
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, []);

  // Table columns
  const columns = [
    {
      key: 'id',
      label: 'ID',
      sortable: true
    },
    {
      key: 'imageUrl',
      label: 'Image',
      sortable: false,
      render: (item) => <ImagePreview src={item.imageUrl} alt={item.title} />
    },
    {
      key: 'title',
      label: 'Title',
      sortable: true
    },
    {
      key: 'discount',
      label: 'Discount',
      sortable: true,
      render: (item) => `${item.discount}%`
    },
    {
      key: 'validUntil',
      label: 'Valid Until',
      sortable: true
    },
    {
      key: 'active',
      label: 'Status',
      sortable: true,
      render: (item) => (
        <Badge type={item.active ? 'success' : 'error'}>
          {item.active ? (
            <>
              <FontAwesomeIcon icon={faCheck} style={{ marginRight: '5px' }} />
              Active
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faTimes} style={{ marginRight: '5px' }} />
              Inactive
            </>
          )}
        </Badge>
      )
    },
    {
      key: 'createdAt',
      label: 'Created At',
      sortable: true
    }
  ];

  // Form fields
  const formFields = [
    {
      type: 'text',
      name: 'title',
      label: 'Title',
      placeholder: 'Enter offer title',
      required: true
    },
    {
      type: 'text',
      name: 'slug',
      label: 'Slug',
      placeholder: 'Enter offer slug',
      required: true
    },
    {
      type: 'textarea',
      name: 'description',
      label: 'Short Description',
      placeholder: 'Enter a brief description (shown on cards)',
      required: true
    },
    {
      type: 'textarea',
      name: 'longDescription',
      label: 'Long Description',
      placeholder: 'Enter detailed description for the detail page',
      required: true
    },
    {
      type: 'number',
      name: 'discount',
      label: 'Discount (%)',
      placeholder: 'Enter discount percentage',
      required: true
    },
    {
      type: 'date',
      name: 'validUntil',
      label: 'Valid Until',
      required: true
    },
    {
      type: 'text',
      name: 'bookingPeriod',
      label: 'Booking Period',
      placeholder: 'e.g. May 1 - August 31, 2023',
      required: false
    },
    {
      type: 'text',
      name: 'stayPeriod',
      label: 'Stay Period',
      placeholder: 'e.g. May 1 - September 30, 2023',
      required: false
    },
    {
      type: 'image',
      name: 'imageUrl',
      label: 'Main Image',
      required: true
    },
    {
      type: 'textarea',
      name: 'features',
      label: 'What\'s Included',
      placeholder: 'Enter features, one per line (e.g. Airport Transfers, Daily Breakfast, etc.)',
      required: true
    },
    {
      type: 'textarea',
      name: 'terms',
      label: 'Terms & Conditions',
      placeholder: 'Enter terms and conditions, one per line',
      required: false
    },
    {
      type: 'textarea',
      name: 'availableDestinations',
      label: 'Available Destinations',
      placeholder: 'Enter destination IDs or slugs, one per line',
      required: false
    },
    {
      type: 'checkbox',
      name: 'active',
      label: 'Active Offer'
    }
  ];

  // Handle add offer
  const handleAddOffer = () => {
    setFormData({});
    setFormErrors({});
    setIsAddModalOpen(true);
  };

  // Handle edit offer
  const handleEditOffer = (offer) => {
    console.log('Editing offer:', offer);
    setSelectedOffer(offer);
    setFormData({ ...offer });
    setFormErrors({});
    setIsEditModalOpen(true);
  };

  // Handle view offer
  const handleViewOffer = (offer) => {
    setSelectedOffer(offer);
    setIsViewModalOpen(true);
  };

  // Handle delete offer
  const handleDeleteOffer = (offer) => {
    setSelectedOffer(offer);
    setIsDeleteModalOpen(true);
  };

  // Handle form submit for add
  const handleAddSubmit = async () => {
    // Validate form
    const errors = {};
    formFields.forEach((field) => {
      if (field.required && !formData[field.name]) {
        errors[field.name] = `${field.label} is required`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      // Handle image upload if it's a File object
      if (formData.imageFile && formData.imageFile instanceof File) {
        const formDataObj = new FormData();
        formDataObj.append('file', formData.imageFile);
        formDataObj.append('title', formData.title);
        formDataObj.append('alt', formData.title);

        const uploadResult = await mediaService.uploadMedia(formDataObj);
        formData.imageUrl = uploadResult.url;
      }

      // Process content fields
      const offerData = {
        ...formData,
        content: formData.longDescription || formData.description,
        discount: parseFloat(formData.discount),
        validFrom: formData.bookingPeriod ? formData.bookingPeriod.split(' - ')[0] : new Date().toISOString().split('T')[0],
        validTo: formData.validUntil,
        featured: formData.active || false
      };

      // Create offer
      const newOffer = await offerService.createOffer(offerData);

      // Update offers list with the mapped format
      const mappedOffer = {
        ...newOffer,
        active: new Date(newOffer.validTo) >= new Date(),
        validUntil: newOffer.validTo
      };

      setOffers([...offers, mappedOffer]);
      setIsAddModalOpen(false);
    } catch (error) {
      console.error('Error creating offer:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  // Handle form submit for edit
  const handleEditSubmit = async () => {
    // Validate form
    const errors = {};
    formFields.forEach((field) => {
      if (field.required && !formData[field.name]) {
        errors[field.name] = `${field.label} is required`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      // Handle image upload if it's a File object
      if (formData.imageFile && formData.imageFile instanceof File) {
        const formDataObj = new FormData();
        formDataObj.append('file', formData.imageFile);
        formDataObj.append('title', formData.title);
        formDataObj.append('alt', formData.title);

        const uploadResult = await mediaService.uploadMedia(formDataObj);
        formData.imageUrl = uploadResult.url;
      }

      // Process content fields
      const offerData = {
        ...formData,
        content: formData.longDescription || formData.description,
        discount: parseFloat(formData.discount),
        validFrom: formData.bookingPeriod ? formData.bookingPeriod.split(' - ')[0] : new Date().toISOString().split('T')[0],
        validTo: formData.validUntil,
        featured: formData.active || false
      };

      // Update offer
      const updatedOffer = await offerService.updateOffer(selectedOffer.id, offerData);

      // Update offers list with the mapped format
      const mappedOffer = {
        ...updatedOffer,
        active: new Date(updatedOffer.validTo) >= new Date(),
        validUntil: updatedOffer.validTo
      };

      const updatedOffers = offers.map((offer) =>
        offer.id === selectedOffer.id ? mappedOffer : offer
      );

      setOffers(updatedOffers);
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('Error updating offer:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  // Handle delete confirm
  const handleDeleteConfirm = async () => {
    try {
      setLoading(true);

      // Delete offer
      await offerService.deleteOffer(selectedOffer.id);

      // Update offers list
      const updatedOffers = offers.filter(
        (offer) => offer.id !== selectedOffer.id
      );

      setOffers(updatedOffers);
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting offer:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <PageHeader
        title="Special Offers"
        subtitle="Manage your promotional offers"
        onAdd={handleAddOffer}
      />

      <DataTable
        title="Special Offers List"
        columns={columns}
        data={offers}
        onView={handleViewOffer}
        onEdit={handleEditOffer}
        onDelete={handleDeleteOffer}
        onAdd={handleAddOffer}
      />

      {/* Add Offer Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add Special Offer"
        size="large"
        hideFooter
      >
        <FormBuilder
          title="Offer Information"
          fields={formFields}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleAddSubmit}
          onCancel={() => setIsAddModalOpen(false)}
          submitLabel="Add Offer"
          loading={loading}
        />
      </Modal>

      {/* Edit Offer Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Special Offer"
        size="large"
        hideFooter
      >
        <FormBuilder
          title="Offer Information"
          fields={formFields}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditModalOpen(false)}
          submitLabel="Update Offer"
          loading={loading}
        />
      </Modal>

      {/* View Offer Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        title="Offer Details"
        size="large"
      >
        {selectedOffer && (
          <div>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
              <div>
                <h2>{selectedOffer.title}</h2>
                <p><strong>Slug:</strong> {selectedOffer.slug}</p>
                <p><strong>Short Description:</strong> {selectedOffer.description}</p>
                <p><strong>Long Description:</strong> {selectedOffer.longDescription || 'Not provided'}</p>
                <p><strong>Discount:</strong> {selectedOffer.discount}%</p>
                <p><strong>Valid Until:</strong> {selectedOffer.validUntil}</p>
                <p><strong>Booking Period:</strong> {selectedOffer.bookingPeriod || 'Not specified'}</p>
                <p><strong>Stay Period:</strong> {selectedOffer.stayPeriod || 'Not specified'}</p>
                <p><strong>Status:</strong> {selectedOffer.active ? 'Active' : 'Inactive'}</p>
                <p><strong>Created At:</strong> {selectedOffer.createdAt}</p>

                {selectedOffer.features && (
                  <>
                    <h3 style={{ marginTop: '20px' }}>What's Included</h3>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                      {typeof selectedOffer.features === 'string'
                        ? selectedOffer.features.split('\n').map((feature, index) => (
                            feature.trim() && <Badge key={index} type="default">{feature.trim()}</Badge>
                          ))
                        : Array.isArray(selectedOffer.features) && selectedOffer.features.map((feature, index) => (
                            <Badge key={index} type="default">{feature}</Badge>
                          ))
                      }
                    </div>
                  </>
                )}

                {selectedOffer.terms && (
                  <>
                    <h3 style={{ marginTop: '20px' }}>Terms & Conditions</h3>
                    <ul style={{ paddingLeft: '20px' }}>
                      {typeof selectedOffer.terms === 'string'
                        ? selectedOffer.terms.split('\n').map((term, index) => (
                            term.trim() && <li key={index}>{term.trim()}</li>
                          ))
                        : Array.isArray(selectedOffer.terms) && selectedOffer.terms.map((term, index) => (
                            <li key={index}>{term}</li>
                          ))
                      }
                    </ul>
                  </>
                )}
              </div>

              <div>
                <h3>Main Image</h3>
                <img
                  src={selectedOffer.imageUrl}
                  alt={selectedOffer.title}
                  style={{ width: '100%', borderRadius: '8px', marginBottom: '20px' }}
                />

                {selectedOffer.availableDestinations && (
                  <>
                    <h3>Available Destinations</h3>
                    <ul style={{ paddingLeft: '20px' }}>
                      {typeof selectedOffer.availableDestinations === 'string'
                        ? selectedOffer.availableDestinations.split('\n').map((destination, index) => (
                            destination.trim() && <li key={index}>{destination.trim()}</li>
                          ))
                        : Array.isArray(selectedOffer.availableDestinations) && selectedOffer.availableDestinations.map((destination, index) => (
                            <li key={index}>{destination}</li>
                          ))
                      }
                    </ul>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Offer Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Special Offer"
        onConfirm={handleDeleteConfirm}
        confirmLabel="Delete"
        confirmVariant="danger"
        loading={loading}
      >
        {selectedOffer && (
          <div>
            <p>Are you sure you want to delete the offer "{selectedOffer.title}"?</p>
            <p>This action cannot be undone.</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OffersPage;
