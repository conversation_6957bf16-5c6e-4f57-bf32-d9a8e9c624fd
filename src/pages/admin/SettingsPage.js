import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faSave,
  faGlobe,
  faEnvelope,
  faPalette,
  faServer,
  faShieldAlt,
  faCheck,
  faTimes
} from '@fortawesome/free-solid-svg-icons';

import PageHeader from '../../components/dashboard/PageHeader';
import { settingsService } from '../../services';

const SettingsContainer = styled.div`
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 30px;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
  }
`;

const SettingsSidebar = styled.div`
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const SidebarNav = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const SidebarItem = styled.li`
  border-bottom: 1px solid #eee;

  &:last-child {
    border-bottom: none;
  }
`;

const SidebarLink = styled.button`
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 15px 20px;
  width: 100%;
  text-align: left;
  background: ${props => props.active ? '#f5f7fb' : 'none'};
  border: none;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #f5f7fb;
  }

  svg {
    color: #0099b8;
    width: 18px;
  }

  span {
    font-weight: ${props => props.active ? '600' : 'normal'};
    color: ${props => props.active ? '#0099b8' : '#333'};
  }
`;

const SettingsContent = styled.div`
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  padding: 30px;
`;

const SettingsHeader = styled.div`
  margin-bottom: 30px;

  h2 {
    margin: 0 0 10px;
    font-size: 24px;
  }

  p {
    margin: 0;
    color: #666;
  }
`;

const SettingsForm = styled.form`
  display: grid;
  gap: 20px;
`;

const FormGroup = styled.div`
  display: grid;
  gap: 8px;
`;

const Label = styled.label`
  font-weight: 500;
  color: #333;
`;

const Input = styled.input`
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #0099b8;
  }
`;

const Textarea = styled.textarea`
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  min-height: 100px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #0099b8;
  }
`;

const Select = styled.select`
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #0099b8;
  }
`;

const Checkbox = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;

  input {
    width: 18px;
    height: 18px;
  }

  label {
    font-weight: normal;
  }
`;

const ColorPicker = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;

  input {
    width: 40px;
    height: 40px;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 4px;
  }
`;

const SettingsFooter = styled.div`
  margin-top: 30px;
  display: flex;
  justify-content: flex-end;
`;

const SaveButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background-color: #0099b8;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover:not(:disabled) {
    background-color: #007a94;
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }

  svg {
    width: 16px;
  }
`;

const SuccessMessage = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 4px;
  margin-bottom: 20px;

  svg {
    width: 16px;
  }
`;

const ErrorMessage = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background-color: #ffebee;
  color: #c62828;
  border-radius: 4px;
  margin-bottom: 20px;

  svg {
    width: 16px;
  }
`;

const SettingsPage = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [settings, setSettings] = useState({});
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [errorMessage, setErrorMessage] = useState('');

  // Fetch settings
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);
        console.log('Fetching settings from SettingsPage component...');
        const data = await settingsService.getAllSettings();
        console.log('Settings data received:', data);

        // Group settings by category
        const groupedSettings = {};
        data.forEach(setting => {
          if (!groupedSettings[setting.group]) {
            groupedSettings[setting.group] = {};
          }
          groupedSettings[setting.group][setting.key] = setting.value;
        });

        console.log('Grouped settings:', groupedSettings);
        setSettings(groupedSettings);
      } catch (error) {
        console.error('Error fetching settings:', error);
        setErrorMessage('Failed to load settings. Please try again. Error: ' + (error.message || 'Unknown error'));
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  // Handle input change
  const handleChange = (group, key, value) => {
    setSettings(prev => ({
      ...prev,
      [group]: {
        ...prev[group],
        [key]: value
      }
    }));
  };

  // Handle form submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    try {
      setSaving(true);
      setSuccessMessage('');
      setErrorMessage('');

      // Flatten settings for API
      const flatSettings = [];
      Object.entries(settings).forEach(([group, groupSettings]) => {
        Object.entries(groupSettings).forEach(([key, value]) => {
          flatSettings.push({ group, key, value });
        });
      });

      console.log('Submitting settings:', flatSettings);

      await settingsService.updateMultipleSettings(flatSettings);
      console.log('Settings updated successfully');
      setSuccessMessage('Settings saved successfully!');

      // Clear success message after 3 seconds
      setTimeout(() => {
        setSuccessMessage('');
      }, 3000);
    } catch (error) {
      console.error('Error saving settings:', error);
      setErrorMessage('Failed to save settings. Please try again. Error: ' + (error.message || 'Unknown error'));
    } finally {
      setSaving(false);
    }
  };

  // Render settings form based on active tab
  const renderSettingsForm = () => {
    if (loading) {
      return <div style={{ textAlign: 'center', padding: '30px 0' }}>Loading settings...</div>;
    }

    switch (activeTab) {
      case 'general':
        return (
          <>
            <SettingsHeader>
              <h2>General Settings</h2>
              <p>Configure basic website information and settings</p>
            </SettingsHeader>

            {successMessage && (
              <SuccessMessage>
                <FontAwesomeIcon icon={faCheck} />
                {successMessage}
              </SuccessMessage>
            )}

            {errorMessage && (
              <ErrorMessage>
                <FontAwesomeIcon icon={faTimes} />
                {errorMessage}
              </ErrorMessage>
            )}

            <SettingsForm onSubmit={handleSubmit}>
              <FormGroup>
                <Label>Site Title</Label>
                <Input
                  type="text"
                  value={settings.general?.siteTitle || ''}
                  onChange={(e) => handleChange('general', 'siteTitle', e.target.value)}
                />
              </FormGroup>

              <FormGroup>
                <Label>Site Description</Label>
                <Textarea
                  value={settings.general?.siteDescription || ''}
                  onChange={(e) => handleChange('general', 'siteDescription', e.target.value)}
                />
              </FormGroup>

              <FormGroup>
                <Label>Site Logo URL</Label>
                <Input
                  type="text"
                  value={settings.general?.logoUrl || ''}
                  onChange={(e) => handleChange('general', 'logoUrl', e.target.value)}
                />
              </FormGroup>

              <FormGroup>
                <Label>Favicon URL</Label>
                <Input
                  type="text"
                  value={settings.general?.faviconUrl || ''}
                  onChange={(e) => handleChange('general', 'faviconUrl', e.target.value)}
                />
              </FormGroup>

              <FormGroup>
                <Label>Default Language</Label>
                <Select
                  value={settings.general?.defaultLanguage || 'en'}
                  onChange={(e) => handleChange('general', 'defaultLanguage', e.target.value)}
                >
                  <option value="en">English</option>
                  <option value="fr">French</option>
                  <option value="es">Spanish</option>
                  <option value="de">German</option>
                  <option value="it">Italian</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Checkbox>
                  <input
                    type="checkbox"
                    checked={settings.general?.maintenanceMode === 'true'}
                    onChange={(e) => handleChange('general', 'maintenanceMode', e.target.checked.toString())}
                  />
                  <label>Maintenance Mode</label>
                </Checkbox>
              </FormGroup>

              <SettingsFooter>
                <SaveButton type="submit" disabled={saving}>
                  <FontAwesomeIcon icon={faSave} />
                  {saving ? 'Saving...' : 'Save Settings'}
                </SaveButton>
              </SettingsFooter>
            </SettingsForm>
          </>
        );

      case 'appearance':
        return (
          <>
            <SettingsHeader>
              <h2>Appearance Settings</h2>
              <p>Customize the look and feel of your website</p>
            </SettingsHeader>

            {successMessage && (
              <SuccessMessage>
                <FontAwesomeIcon icon={faCheck} />
                {successMessage}
              </SuccessMessage>
            )}

            {errorMessage && (
              <ErrorMessage>
                <FontAwesomeIcon icon={faTimes} />
                {errorMessage}
              </ErrorMessage>
            )}

            <SettingsForm onSubmit={handleSubmit}>
              <FormGroup>
                <Label>Primary Color</Label>
                <ColorPicker>
                  <input
                    type="color"
                    value={settings.appearance?.primaryColor || '#0099b8'}
                    onChange={(e) => handleChange('appearance', 'primaryColor', e.target.value)}
                  />
                  <span>{settings.appearance?.primaryColor || '#0099b8'}</span>
                </ColorPicker>
              </FormGroup>

              <FormGroup>
                <Label>Secondary Color</Label>
                <ColorPicker>
                  <input
                    type="color"
                    value={settings.appearance?.secondaryColor || '#ff9800'}
                    onChange={(e) => handleChange('appearance', 'secondaryColor', e.target.value)}
                  />
                  <span>{settings.appearance?.secondaryColor || '#ff9800'}</span>
                </ColorPicker>
              </FormGroup>

              <FormGroup>
                <Label>Font Family</Label>
                <Select
                  value={settings.appearance?.fontFamily || 'Inter'}
                  onChange={(e) => handleChange('appearance', 'fontFamily', e.target.value)}
                >
                  <option value="Inter">Inter</option>
                  <option value="Roboto">Roboto</option>
                  <option value="Open Sans">Open Sans</option>
                  <option value="Montserrat">Montserrat</option>
                  <option value="Poppins">Poppins</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>Heading Font Family</Label>
                <Select
                  value={settings.appearance?.headingFontFamily || 'Cormorant Garamond'}
                  onChange={(e) => handleChange('appearance', 'headingFontFamily', e.target.value)}
                >
                  <option value="Cormorant Garamond">Cormorant Garamond</option>
                  <option value="Playfair Display">Playfair Display</option>
                  <option value="Merriweather">Merriweather</option>
                  <option value="Lora">Lora</option>
                  <option value="Libre Baskerville">Libre Baskerville</option>
                </Select>
              </FormGroup>

              <FormGroup>
                <Label>Button Style</Label>
                <Select
                  value={settings.appearance?.buttonStyle || 'rounded'}
                  onChange={(e) => handleChange('appearance', 'buttonStyle', e.target.value)}
                >
                  <option value="rounded">Rounded</option>
                  <option value="square">Square</option>
                  <option value="pill">Pill</option>
                </Select>
              </FormGroup>

              <SettingsFooter>
                <SaveButton type="submit" disabled={saving}>
                  <FontAwesomeIcon icon={faSave} />
                  {saving ? 'Saving...' : 'Save Settings'}
                </SaveButton>
              </SettingsFooter>
            </SettingsForm>
          </>
        );

      // Add more tabs as needed

      default:
        return null;
    }
  };

  return (
    <div>
      <PageHeader
        title="Settings"
        subtitle="Configure your website settings"
      />

      <SettingsContainer>
        <SettingsSidebar>
          <SidebarNav>
            <SidebarItem>
              <SidebarLink
                active={activeTab === 'general'}
                onClick={() => setActiveTab('general')}
              >
                <FontAwesomeIcon icon={faGlobe} />
                <span>General</span>
              </SidebarLink>
            </SidebarItem>

            <SidebarItem>
              <SidebarLink
                active={activeTab === 'appearance'}
                onClick={() => setActiveTab('appearance')}
              >
                <FontAwesomeIcon icon={faPalette} />
                <span>Appearance</span>
              </SidebarLink>
            </SidebarItem>

            <SidebarItem>
              <SidebarLink
                active={activeTab === 'email'}
                onClick={() => setActiveTab('email')}
              >
                <FontAwesomeIcon icon={faEnvelope} />
                <span>Email</span>
              </SidebarLink>
            </SidebarItem>

            <SidebarItem>
              <SidebarLink
                active={activeTab === 'api'}
                onClick={() => setActiveTab('api')}
              >
                <FontAwesomeIcon icon={faServer} />
                <span>API</span>
              </SidebarLink>
            </SidebarItem>

            <SidebarItem>
              <SidebarLink
                active={activeTab === 'security'}
                onClick={() => setActiveTab('security')}
              >
                <FontAwesomeIcon icon={faShieldAlt} />
                <span>Security</span>
              </SidebarLink>
            </SidebarItem>
          </SidebarNav>
        </SettingsSidebar>

        <SettingsContent>
          {renderSettingsForm()}
        </SettingsContent>
      </SettingsContainer>
    </div>
  );
};

export default SettingsPage;
