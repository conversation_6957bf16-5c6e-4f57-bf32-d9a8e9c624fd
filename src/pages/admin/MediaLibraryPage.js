import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faUpload,
  faImage,
  faVideo,
  faFile,
  faTrash,
  faEdit,
  faSearch,
  faFilter,
  faCheck,
  faTimes
} from '@fortawesome/free-solid-svg-icons';

import PageHeader from '../../components/dashboard/PageHeader';
import Modal from '../../components/dashboard/Modal';
import FormBuilder from '../../components/dashboard/FormBuilder';
import { mediaService } from '../../services';

const MediaGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
`;

const MediaCard = styled.div`
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
  position: relative;
  transition: transform 0.3s, box-shadow 0.3s;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
`;

const MediaPreview = styled.div`
  height: 150px;
  background-color: #f5f7fb;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  svg {
    font-size: 40px;
    color: #ccc;
  }
`;

const MediaInfo = styled.div`
  padding: 12px;
`;

const MediaTitle = styled.h4`
  margin: 0 0 5px;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const MediaMeta = styled.div`
  font-size: 12px;
  color: #666;
  margin-bottom: 10px;
`;

const MediaActions = styled.div`
  display: flex;
  justify-content: space-between;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  color: ${props => props.color || '#333'};
  cursor: pointer;
  padding: 5px;
  font-size: 14px;
  transition: color 0.3s;

  &:hover {
    color: ${props => props.hoverColor || '#000'};
  }
`;

const UploadArea = styled.div`
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 30px;
  text-align: center;
  margin-bottom: 30px;
  transition: border-color 0.3s, background-color 0.3s;
  cursor: pointer;

  &:hover {
    border-color: #0099b8;
    background-color: rgba(0, 153, 184, 0.05);
  }

  svg {
    font-size: 40px;
    color: #0099b8;
    margin-bottom: 15px;
  }

  h3 {
    margin: 0 0 10px;
    color: #333;
  }

  p {
    color: #666;
    margin: 0;
  }
`;

const FilterBar = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  background-color: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
`;

const SearchInput = styled.div`
  position: relative;
  flex: 1;
  max-width: 300px;

  svg {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
  }

  input {
    width: 100%;
    padding: 10px 10px 10px 35px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #0099b8;
    }
  }
`;

const FilterDropdown = styled.div`
  position: relative;
  margin-left: 15px;
`;

const FilterButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #f5f7fb;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background-color: #e9ecef;
  }

  svg {
    color: #666;
  }
`;

const MediaLibraryPage = () => {
  const [mediaItems, setMediaItems] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterType, setFilterType] = useState('all');
  const [selectedMedia, setSelectedMedia] = useState(null);
  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [uploadFiles, setUploadFiles] = useState([]);
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});

  // Fetch media items
  useEffect(() => {
    const fetchMedia = async () => {
      try {
        setLoading(true);
        const data = await mediaService.getAllMedia();
        setMediaItems(data);
      } catch (error) {
        console.error('Error fetching media:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMedia();
  }, []);

  // Filter media items
  const filteredMedia = mediaItems.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.alt.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = filterType === 'all' || item.type === filterType;
    return matchesSearch && matchesType;
  });

  // Handle file upload
  const handleFileChange = (e) => {
    setUploadFiles(Array.from(e.target.files));
  };

  // Handle upload submit
  const handleUploadSubmit = async () => {
    try {
      setLoading(true);

      for (const file of uploadFiles) {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('title', file.name);
        formData.append('alt', file.name || 'Image');

        console.log('Uploading file:', file.name);
        const result = await mediaService.uploadMedia(formData);
        console.log('Upload result:', result);
      }

      // Refresh media list
      console.log('Refreshing media list...');
      const data = await mediaService.getAllMedia();
      console.log('Media list refreshed:', data);
      setMediaItems(data);
      setIsUploadModalOpen(false);
      setUploadFiles([]);
    } catch (error) {
      console.error('Error uploading files:', error);
      alert(`Error uploading files: ${error.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle edit media
  const handleEditMedia = (media) => {
    setSelectedMedia(media);
    setFormData({
      title: media.title,
      alt: media.alt,
      description: media.description
    });
    setFormErrors({});
    setIsEditModalOpen(true);
  };

  // Handle edit submit
  const handleEditSubmit = async () => {
    try {
      setLoading(true);
      await mediaService.updateMedia(selectedMedia.id, formData);

      // Refresh media list
      const data = await mediaService.getAllMedia();
      setMediaItems(data);
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('Error updating media:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete media
  const handleDeleteMedia = (media) => {
    setSelectedMedia(media);
    setIsDeleteModalOpen(true);
  };

  // Handle delete confirm
  const handleDeleteConfirm = async () => {
    try {
      setLoading(true);
      await mediaService.deleteMedia(selectedMedia.id);

      // Refresh media list
      const data = await mediaService.getAllMedia();
      setMediaItems(data);
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting media:', error);
    } finally {
      setLoading(false);
    }
  };

  // Form fields for edit modal
  const formFields = [
    {
      type: 'text',
      name: 'title',
      label: 'Title',
      placeholder: 'Enter media title',
      required: true
    },
    {
      type: 'text',
      name: 'alt',
      label: 'Alt Text',
      placeholder: 'Enter alternative text for accessibility',
      required: true
    },
    {
      type: 'textarea',
      name: 'description',
      label: 'Description',
      placeholder: 'Enter media description',
      required: false
    }
  ];

  return (
    <div>
      <PageHeader
        title="Media Library"
        subtitle="Manage your images, videos, and files"
        onAdd={() => setIsUploadModalOpen(true)}
        addLabel="Upload Media"
      />

      <UploadArea onClick={() => setIsUploadModalOpen(true)}>
        <FontAwesomeIcon icon={faUpload} />
        <h3>Upload Media Files</h3>
        <p>Click to upload images, videos, or documents</p>
      </UploadArea>

      <FilterBar>
        <SearchInput>
          <FontAwesomeIcon icon={faSearch} />
          <input
            type="text"
            placeholder="Search media..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </SearchInput>

        <FilterDropdown>
          <FilterButton onClick={() => setFilterType('all')}>
            <FontAwesomeIcon icon={faFilter} />
            {filterType === 'all' ? 'All Media' :
             filterType === 'image' ? 'Images' :
             filterType === 'video' ? 'Videos' : 'Documents'}
          </FilterButton>
        </FilterDropdown>
      </FilterBar>

      {loading ? (
        <div style={{ textAlign: 'center', padding: '30px 0' }}>Loading media...</div>
      ) : (
        <MediaGrid>
          {filteredMedia.map(media => (
            <MediaCard key={media.id}>
              <MediaPreview>
                {media.type === 'image' ? (
                  <img src={media.url} alt={media.alt} />
                ) : media.type === 'video' ? (
                  <FontAwesomeIcon icon={faVideo} />
                ) : (
                  <FontAwesomeIcon icon={faFile} />
                )}
              </MediaPreview>

              <MediaInfo>
                <MediaTitle>{media.title}</MediaTitle>
                <MediaMeta>
                  {media.type} • {media.size} • {media.createdAt}
                </MediaMeta>

                <MediaActions>
                  <ActionButton
                    onClick={() => handleEditMedia(media)}
                    color="#0099b8"
                    hoverColor="#007a94"
                  >
                    <FontAwesomeIcon icon={faEdit} />
                  </ActionButton>

                  <ActionButton
                    onClick={() => handleDeleteMedia(media)}
                    color="#f44336"
                    hoverColor="#d32f2f"
                  >
                    <FontAwesomeIcon icon={faTrash} />
                  </ActionButton>
                </MediaActions>
              </MediaInfo>
            </MediaCard>
          ))}
        </MediaGrid>
      )}

      {/* Upload Modal */}
      <Modal
        isOpen={isUploadModalOpen}
        onClose={() => setIsUploadModalOpen(false)}
        title="Upload Media"
        size="medium"
        hideFooter
      >
        <div style={{ padding: '20px' }}>
          <input
            type="file"
            multiple
            onChange={handleFileChange}
            style={{ marginBottom: '20px' }}
          />

          {uploadFiles.length > 0 && (
            <div>
              <h4>Selected Files:</h4>
              <ul>
                {uploadFiles.map((file, index) => (
                  <li key={index}>{file.name} ({(file.size / 1024).toFixed(2)} KB)</li>
                ))}
              </ul>
            </div>
          )}

          <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '20px' }}>
            <button
              onClick={() => setIsUploadModalOpen(false)}
              style={{
                marginRight: '10px',
                padding: '8px 15px',
                background: 'none',
                border: '1px solid #ddd',
                borderRadius: '4px',
                cursor: 'pointer'
              }}
            >
              Cancel
            </button>

            <button
              onClick={handleUploadSubmit}
              disabled={uploadFiles.length === 0 || loading}
              style={{
                padding: '8px 15px',
                backgroundColor: '#0099b8',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
                opacity: uploadFiles.length === 0 || loading ? 0.7 : 1
              }}
            >
              {loading ? 'Uploading...' : 'Upload Files'}
            </button>
          </div>
        </div>
      </Modal>

      {/* Edit Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Media"
        size="medium"
        hideFooter
      >
        <FormBuilder
          fields={formFields}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditModalOpen(false)}
          submitLabel="Update Media"
          loading={loading}
        />
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Media"
        size="small"
        onConfirm={handleDeleteConfirm}
        confirmLabel="Delete"
        confirmVariant="danger"
        loading={loading}
      >
        <div style={{ padding: '20px 0' }}>
          <p>Are you sure you want to delete <strong>{selectedMedia?.title}</strong>?</p>
          <p>This action cannot be undone.</p>
        </div>
      </Modal>
    </div>
  );
};

export default MediaLibraryPage;
