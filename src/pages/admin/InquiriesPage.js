import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faEnvelope,
  faEnvelopeOpen,
  faReply,
  faTrash,
  faStar,
  faExclamationCircle,
  faCheck
} from '@fortawesome/free-solid-svg-icons';
import { faStar as faStarRegular } from '@fortawesome/free-regular-svg-icons';
import PageHeader from '../../components/dashboard/PageHeader';
import Modal from '../../components/dashboard/Modal';
import { inquiryService } from '../../services';

const InquiriesContainer = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 20px;
  height: calc(100vh - 200px);

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    height: auto;
  }
`;

const InquiriesSidebar = styled.div`
  background-color: #f9f9f9;
  border-radius: 8px;
  overflow: hidden;

  @media (max-width: 768px) {
    display: ${({ isOpen }) => (isOpen ? 'block' : 'none')};
  }
`;

const SidebarHeader = styled.div`
  padding: 15px;
  background-color: #f1f1f1;
  border-bottom: 1px solid #ddd;

  h3 {
    margin: 0;
    font-size: 16px;
    color: #333;
  }
`;

const InquiryList = styled.div`
  height: calc(100% - 50px);
  overflow-y: auto;
`;

const InquiryItem = styled.div`
  padding: 15px;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  background-color: ${({ isRead, isActive }) =>
    isActive ? '#e3f2fd' : isRead ? 'transparent' : '#fff'};

  &:hover {
    background-color: ${({ isActive }) => isActive ? '#e3f2fd' : '#f5f5f5'};
  }
`;

const InquiryName = styled.div`
  font-weight: ${({ isRead }) => isRead ? '400' : '600'};
  margin-bottom: 5px;
  display: flex;
  justify-content: space-between;

  span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`;

const InquirySubject = styled.div`
  font-size: 14px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const InquiryDate = styled.div`
  font-size: 12px;
  color: #999;
  margin-top: 5px;
`;

const InquiryContent = styled.div`
  background-color: white;
  border-radius: 8px;
  height: 100%;
  display: flex;
  flex-direction: column;

  @media (max-width: 768px) {
    display: ${({ isOpen }) => (isOpen ? 'flex' : 'none')};
  }
`;

const ContentHeader = styled.div`
  padding: 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ContentTitle = styled.div`
  h2 {
    margin: 0 0 5px;
    font-size: 20px;
  }

  p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
`;

const ContentActions = styled.div`
  display: flex;
  gap: 10px;
`;

const ActionButton = styled.button`
  background-color: ${({ color }) => color || '#f1f1f1'};
  color: ${({ textColor }) => textColor || '#333'};
  border: none;
  border-radius: 4px;
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;

  &:hover {
    opacity: 0.9;
  }
`;

const MessageBody = styled.div`
  padding: 20px;
  flex: 1;
  overflow-y: auto;

  p {
    line-height: 1.6;
    color: #333;
  }
`;

const MessageInfo = styled.div`
  padding: 15px 20px;
  background-color: #f9f9f9;
  border-top: 1px solid #eee;

  p {
    margin: 5px 0;
    font-size: 14px;
    color: #666;
  }
`;

const ReplyForm = styled.div`
  padding: 20px;
  border-top: 1px solid #eee;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 150px;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-family: inherit;
  font-size: 14px;

  &:focus {
    outline: none;
    border-color: #0099b8;
  }
`;

const FormActions = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 15px;
  gap: 10px;
`;

const EmptyState = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;

  svg {
    font-size: 48px;
    margin-bottom: 15px;
    color: #ddd;
  }

  h3 {
    margin: 0 0 10px;
  }

  p {
    margin: 0;
    text-align: center;
  }
`;

const LoadingIndicator = styled.div`
  text-align: center;
  padding: 20px;
  color: #666;
`;

const InquiriesPage = () => {
  const [inquiries, setInquiries] = useState([]);
  const [selectedInquiry, setSelectedInquiry] = useState(null);
  const [replyText, setReplyText] = useState('');
  const [loading, setLoading] = useState(true);
  const [showReplyForm, setShowReplyForm] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);
  const [showList, setShowList] = useState(true);
  const [showContent, setShowContent] = useState(false);
  const [isReplyModalOpen, setIsReplyModalOpen] = useState(false);

  useEffect(() => {
    // Handle window resize
    const handleResize = () => {
      const mobile = window.innerWidth <= 768;
      setIsMobile(mobile);
      if (!mobile) {
        setShowList(true);
        setShowContent(true);
      }
    };

    window.addEventListener('resize', handleResize);

    // Fetch inquiries from API
    const fetchInquiries = async () => {
      try {
        setLoading(true);
        const data = await inquiryService.getAllInquiries();

        // Map API data to component format
        const mappedInquiries = data.map(inquiry => ({
          id: inquiry.id,
          name: inquiry.name,
          email: inquiry.email,
          subject: inquiry.subject,
          message: inquiry.message,
          date: new Date(inquiry.createdAt).toLocaleString(),
          isRead: inquiry.status !== 'unread',
          isStarred: false, // API doesn't have this feature yet
          phone: inquiry.phone,
          status: inquiry.status,
          reply: inquiry.reply,
          repliedAt: inquiry.repliedAt
        }));

        setInquiries(mappedInquiries);
      } catch (error) {
        console.error('Error fetching inquiries:', error);
        // Show error message to user
      } finally {
        setLoading(false);
      }
    };

    fetchInquiries();

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleSelectInquiry = async (inquiry) => {
    // Mark as read if not already read
    if (!inquiry.isRead) {
      try {
        await inquiryService.markInquiryAsRead(inquiry.id);

        const updatedInquiries = inquiries.map(item =>
          item.id === inquiry.id ? { ...item, isRead: true, status: 'read' } : item
        );
        setInquiries(updatedInquiries);
      } catch (error) {
        console.error('Error marking inquiry as read:', error);
      }
    }

    setSelectedInquiry(inquiry);
    setReplyText('');
    setShowReplyForm(false);

    if (isMobile) {
      setShowList(false);
      setShowContent(true);
    }
  };

  const handleStarInquiry = (e, inquiryId) => {
    e.stopPropagation();
    const updatedInquiries = inquiries.map(inquiry =>
      inquiry.id === inquiryId
        ? { ...inquiry, isStarred: !inquiry.isStarred }
        : inquiry
    );
    setInquiries(updatedInquiries);
  };

  const handleDeleteInquiry = async (inquiryId) => {
    if (window.confirm('Are you sure you want to delete this inquiry?')) {
      try {
        await inquiryService.deleteInquiry(inquiryId);

        const updatedInquiries = inquiries.filter(inquiry => inquiry.id !== inquiryId);
        setInquiries(updatedInquiries);

        if (selectedInquiry && selectedInquiry.id === inquiryId) {
          setSelectedInquiry(null);
          if (isMobile) {
            setShowList(true);
            setShowContent(false);
          }
        }
      } catch (error) {
        console.error('Error deleting inquiry:', error);
        alert('Failed to delete inquiry. Please try again.');
      }
    }
  };

  const handleReply = () => {
    if (isMobile) {
      setIsReplyModalOpen(true);
    } else {
      setShowReplyForm(true);
    }
  };

  const handleSendReply = async () => {
    if (!replyText.trim()) {
      alert('Please enter a reply message');
      return;
    }

    try {
      // Send reply to API
      await inquiryService.replyToInquiry(selectedInquiry.id, { reply: replyText });

      // Update local state
      const updatedInquiries = inquiries.map(inquiry =>
        inquiry.id === selectedInquiry.id
          ? {
              ...inquiry,
              isRead: true,
              status: 'replied',
              reply: replyText,
              repliedAt: new Date().toISOString()
            }
          : inquiry
      );

      // Update selected inquiry
      setSelectedInquiry({
        ...selectedInquiry,
        isRead: true,
        status: 'replied',
        reply: replyText,
        repliedAt: new Date().toISOString()
      });

      setInquiries(updatedInquiries);
      setReplyText('');
      setShowReplyForm(false);
      setIsReplyModalOpen(false);

      alert(`Reply sent to ${selectedInquiry.email}`);
    } catch (error) {
      console.error('Error sending reply:', error);
      alert('Failed to send reply. Please try again.');
    }
  };

  const handleBackToList = () => {
    setShowList(true);
    setShowContent(false);
  };

  if (loading) {
    return <LoadingIndicator>Loading inquiries...</LoadingIndicator>;
  }

  return (
    <div>
      <PageHeader
        title="Inquiries"
        subtitle="Manage customer inquiries and messages"
        backTo={isMobile && showContent ? () => handleBackToList() : undefined}
      />

      <InquiriesContainer>
        <InquiriesSidebar isOpen={showList}>
          <SidebarHeader>
            <h3>Inbox ({inquiries.filter(i => !i.isRead).length} unread)</h3>
          </SidebarHeader>
          <InquiryList>
            {inquiries.length === 0 ? (
              <EmptyState>
                <p>No inquiries found</p>
              </EmptyState>
            ) : (
              inquiries.map(inquiry => (
                <InquiryItem
                  key={inquiry.id}
                  isRead={inquiry.isRead}
                  isActive={selectedInquiry && selectedInquiry.id === inquiry.id}
                  onClick={() => handleSelectInquiry(inquiry)}
                >
                  <InquiryName isRead={inquiry.isRead}>
                    <span>{inquiry.name}</span>
                    <FontAwesomeIcon
                      icon={inquiry.isStarred ? faStar : faStarRegular}
                      color={inquiry.isStarred ? '#ffc107' : '#ccc'}
                      onClick={(e) => handleStarInquiry(e, inquiry.id)}
                    />
                  </InquiryName>
                  <InquirySubject>{inquiry.subject}</InquirySubject>
                  <InquiryDate>{inquiry.date}</InquiryDate>
                </InquiryItem>
              ))
            )}
          </InquiryList>
        </InquiriesSidebar>

        <InquiryContent isOpen={showContent}>
          {selectedInquiry ? (
            <>
              <ContentHeader>
                <ContentTitle>
                  <h2>{selectedInquiry.subject}</h2>
                  <p>From: {selectedInquiry.name} ({selectedInquiry.email})</p>
                </ContentTitle>
                <ContentActions>
                  <ActionButton
                    color="#0099b8"
                    textColor="white"
                    onClick={handleReply}
                  >
                    <FontAwesomeIcon icon={faReply} />
                    Reply
                  </ActionButton>
                  <ActionButton
                    color="#f44336"
                    textColor="white"
                    onClick={() => handleDeleteInquiry(selectedInquiry.id)}
                  >
                    <FontAwesomeIcon icon={faTrash} />
                    Delete
                  </ActionButton>
                </ContentActions>
              </ContentHeader>

              <MessageBody>
                <p>{selectedInquiry.message}</p>
              </MessageBody>

              <MessageInfo>
                <p><strong>Date:</strong> {selectedInquiry.date}</p>
                <p><strong>Email:</strong> {selectedInquiry.email}</p>
                {selectedInquiry.phone && (
                  <p><strong>Phone:</strong> {selectedInquiry.phone}</p>
                )}
              </MessageInfo>

              {showReplyForm && !isMobile && (
                <ReplyForm>
                  <TextArea
                    placeholder="Type your reply here..."
                    value={replyText}
                    onChange={(e) => setReplyText(e.target.value)}
                  />
                  <FormActions>
                    <ActionButton
                      onClick={() => setShowReplyForm(false)}
                    >
                      Cancel
                    </ActionButton>
                    <ActionButton
                      color="#0099b8"
                      textColor="white"
                      onClick={handleSendReply}
                    >
                      <FontAwesomeIcon icon={faCheck} />
                      Send Reply
                    </ActionButton>
                  </FormActions>
                </ReplyForm>
              )}
            </>
          ) : (
            <EmptyState>
              <FontAwesomeIcon icon={faEnvelopeOpen} />
              <h3>No message selected</h3>
              <p>Select a message from the list to view its contents</p>
            </EmptyState>
          )}
        </InquiryContent>
      </InquiriesContainer>

      {/* Reply Modal for Mobile */}
      <Modal
        isOpen={isReplyModalOpen}
        onClose={() => setIsReplyModalOpen(false)}
        title={`Reply to ${selectedInquiry?.name}`}
        size="medium"
        hideFooter
      >
        <div>
          <p><strong>Subject:</strong> Re: {selectedInquiry?.subject}</p>
          <p><strong>To:</strong> {selectedInquiry?.email}</p>
          <TextArea
            placeholder="Type your reply here..."
            value={replyText}
            onChange={(e) => setReplyText(e.target.value)}
          />
          <FormActions>
            <ActionButton
              onClick={() => setIsReplyModalOpen(false)}
            >
              Cancel
            </ActionButton>
            <ActionButton
              color="#0099b8"
              textColor="white"
              onClick={handleSendReply}
            >
              <FontAwesomeIcon icon={faCheck} />
              Send Reply
            </ActionButton>
          </FormActions>
        </div>
      </Modal>
    </div>
  );
};

export default InquiriesPage;
