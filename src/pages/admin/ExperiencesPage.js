import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';

import PageHeader from '../../components/dashboard/PageHeader';
import DataTable from '../../components/dashboard/DataTable';
import Modal from '../../components/dashboard/Modal';
import FormBuilder from '../../components/dashboard/FormBuilder';
import { experienceService, mediaService } from '../../services';

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${({ type }) => {
    switch (type) {
      case 'success':
        return '#e8f5e9';
      case 'error':
        return '#ffebee';
      default:
        return '#e3f2fd';
    }
  }};
  color: ${({ type }) => {
    switch (type) {
      case 'success':
        return '#388e3c';
      case 'error':
        return '#d32f2f';
      default:
        return '#1976d2';
    }
  }};
`;

const ImagePreview = styled.img`
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
`;

const ExperiencesPage = () => {
  // State for experiences data
  const [experiences, setExperiences] = useState([]);
  const [loading, setLoading] = useState(true);

  // State for modals
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // State for form data
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});
  const [selectedExperience, setSelectedExperience] = useState(null);

  // Fetch experiences from API
  useEffect(() => {
    const fetchExperiences = async () => {
      try {
        setLoading(true);
        const data = await experienceService.getAllExperiences();
        setExperiences(data);
      } catch (error) {
        console.error('Error fetching experiences:', error);
        // Show error message to user
      } finally {
        setLoading(false);
      }
    };

    fetchExperiences();
  }, []);

  // Table columns
  const columns = [
    {
      key: 'id',
      label: 'ID',
      sortable: true
    },
    {
      key: 'imageUrl',
      label: 'Image',
      sortable: false,
      render: (item) => <ImagePreview src={item.imageUrl} alt={item.title} />
    },
    {
      key: 'title',
      label: 'Title',
      sortable: true
    },
    {
      key: 'duration',
      label: 'Duration',
      sortable: true
    },
    {
      key: 'price',
      label: 'Price',
      sortable: true,
      render: (item) => `$${item.price}`
    },
    {
      key: 'category',
      label: 'Category',
      sortable: true,
      render: (item) => {
        const categoryLabels = {
          'water-activities': 'Water Activities',
          'wellness': 'Wellness',
          'excursions': 'Excursions',
          'culinary': 'Culinary',
          'beach': 'Beach Activities',
          'fishing': 'Fishing'
        };
        return categoryLabels[item.category] || item.category || 'Not specified';
      }
    },
    {
      key: 'featured',
      label: 'Featured',
      sortable: true,
      render: (item) => (
        <Badge type={item.featured ? 'success' : 'error'}>
          {item.featured ? (
            <>
              <FontAwesomeIcon icon={faCheck} style={{ marginRight: '5px' }} />
              Yes
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faTimes} style={{ marginRight: '5px' }} />
              No
            </>
          )}
        </Badge>
      )
    },
    {
      key: 'createdAt',
      label: 'Created At',
      sortable: true
    }
  ];

  // Form fields
  const formFields = [
    {
      type: 'text',
      name: 'title',
      label: 'Title',
      placeholder: 'Enter experience title',
      required: true
    },
    {
      type: 'text',
      name: 'slug',
      label: 'Slug',
      placeholder: 'Enter experience slug',
      required: true
    },
    {
      type: 'select',
      name: 'category',
      label: 'Category',
      options: [
        { value: 'water-activities', label: 'Water Activities' },
        { value: 'wellness', label: 'Wellness' },
        { value: 'excursions', label: 'Excursions' },
        { value: 'culinary', label: 'Culinary' },
        { value: 'beach', label: 'Beach Activities' },
        { value: 'fishing', label: 'Fishing' }
      ],
      required: true
    },
    {
      type: 'textarea',
      name: 'description',
      label: 'Short Description',
      placeholder: 'Enter a brief description (shown on cards)',
      required: true
    },
    {
      type: 'textarea',
      name: 'longDescription',
      label: 'Long Description',
      placeholder: 'Enter detailed description for the detail page',
      required: true
    },
    {
      type: 'text',
      name: 'duration',
      label: 'Duration',
      placeholder: 'e.g. 2 hours',
      required: true
    },
    {
      type: 'number',
      name: 'price',
      label: 'Price (USD)',
      placeholder: 'Enter price',
      required: true
    },
    {
      type: 'text',
      name: 'groupSize',
      label: 'Group Size',
      placeholder: 'e.g. 1-8 people',
      required: false
    },
    {
      type: 'text',
      name: 'difficulty',
      label: 'Difficulty',
      placeholder: 'e.g. All levels, Beginner, Advanced',
      required: false
    },
    {
      type: 'image',
      name: 'imageUrl',
      label: 'Main Image',
      required: true
    },
    {
      type: 'textarea',
      name: 'features',
      label: 'Features/What\'s Included',
      placeholder: 'Enter features, one per line (e.g. Equipment, Guide, etc.)',
      required: true
    },
    {
      type: 'textarea',
      name: 'gallery',
      label: 'Gallery Images',
      placeholder: 'Enter image URLs, one per line',
      required: false
    },
    {
      type: 'textarea',
      name: 'itinerary',
      label: 'Itinerary',
      placeholder: 'Enter itinerary items in format: "Time | Title | Description", one per line',
      required: false
    },
    {
      type: 'checkbox',
      name: 'featured',
      label: 'Featured Experience'
    }
  ];

  // Handle add experience
  const handleAddExperience = () => {
    setFormData({});
    setFormErrors({});
    setIsAddModalOpen(true);
  };

  // Handle edit experience
  const handleEditExperience = (experience) => {
    console.log('Editing experience:', experience);
    setSelectedExperience(experience);
    setFormData({ ...experience });
    setFormErrors({});
    setIsEditModalOpen(true);
  };

  // Handle view experience
  const handleViewExperience = (experience) => {
    setSelectedExperience(experience);
    setIsViewModalOpen(true);
  };

  // Handle delete experience
  const handleDeleteExperience = (experience) => {
    setSelectedExperience(experience);
    setIsDeleteModalOpen(true);
  };

  // Handle form submit for add
  const handleAddSubmit = async () => {
    // Validate form
    const errors = {};
    formFields.forEach((field) => {
      if (field.required && !formData[field.name]) {
        errors[field.name] = `${field.label} is required`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      // Handle image upload if it's a File object
      if (formData.imageFile && formData.imageFile instanceof File) {
        const formDataObj = new FormData();
        formDataObj.append('file', formData.imageFile);
        formDataObj.append('title', formData.title);
        formDataObj.append('alt', formData.title);

        const uploadResult = await mediaService.uploadMedia(formDataObj);
        formData.imageUrl = uploadResult.url;
      }

      // Process content fields
      const experienceData = {
        ...formData,
        content: formData.longDescription || formData.description,
        price: parseFloat(formData.price)
      };

      // Create experience
      const newExperience = await experienceService.createExperience(experienceData);

      // Update experiences list
      setExperiences([...experiences, newExperience]);
      setIsAddModalOpen(false);
    } catch (error) {
      console.error('Error creating experience:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  // Handle form submit for edit
  const handleEditSubmit = async () => {
    // Validate form
    const errors = {};
    formFields.forEach((field) => {
      if (field.required && !formData[field.name]) {
        errors[field.name] = `${field.label} is required`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);
      console.log('Submitting edit for experience:', selectedExperience.id);
      console.log('Form data:', formData);

      // Handle image upload if it's a File object
      if (formData.imageFile && formData.imageFile instanceof File) {
        console.log('Uploading new image file');
        const formDataObj = new FormData();
        formDataObj.append('file', formData.imageFile);
        formDataObj.append('title', formData.title);
        formDataObj.append('alt', formData.title);

        const uploadResult = await mediaService.uploadMedia(formDataObj);
        console.log('Image upload result:', uploadResult);
        formData.imageUrl = uploadResult.url;
      }

      // Process content fields
      const experienceData = {
        ...formData,
        content: formData.longDescription || formData.description,
        price: parseFloat(formData.price)
      };

      console.log('Processed experience data:', experienceData);

      // Update experience
      console.log('Calling updateExperience with ID:', selectedExperience.id);
      const updatedExperience = await experienceService.updateExperience(selectedExperience.id, experienceData);
      console.log('Update successful, result:', updatedExperience);

      // Update experiences list
      const updatedExperiences = experiences.map((experience) =>
        experience.id === selectedExperience.id ? updatedExperience : experience
      );

      setExperiences(updatedExperiences);
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('Error updating experience:', error);
      alert(`Error updating experience: ${error.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete confirm
  const handleDeleteConfirm = async () => {
    try {
      setLoading(true);

      // Delete experience
      await experienceService.deleteExperience(selectedExperience.id);

      // Update experiences list
      const updatedExperiences = experiences.filter(
        (experience) => experience.id !== selectedExperience.id
      );

      setExperiences(updatedExperiences);
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting experience:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <PageHeader
        title="Experiences"
        subtitle="Manage your experience offerings"
        onAdd={handleAddExperience}
      />

      <DataTable
        title="Experiences List"
        columns={columns}
        data={experiences}
        onView={handleViewExperience}
        onEdit={handleEditExperience}
        onDelete={handleDeleteExperience}
        onAdd={handleAddExperience}
      />

      {/* Add Experience Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add Experience"
        size="large"
        hideFooter
      >
        <FormBuilder
          title="Experience Information"
          fields={formFields}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleAddSubmit}
          onCancel={() => setIsAddModalOpen(false)}
          submitLabel="Add Experience"
          loading={loading}
        />
      </Modal>

      {/* Edit Experience Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Experience"
        size="large"
        hideFooter
      >
        <FormBuilder
          title="Experience Information"
          fields={formFields}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditModalOpen(false)}
          submitLabel="Update Experience"
          loading={loading}
        />
      </Modal>

      {/* View Experience Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        title="Experience Details"
        size="large"
      >
        {selectedExperience && (
          <div>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
              <div>
                <h2>{selectedExperience.title}</h2>
                <p><strong>Slug:</strong> {selectedExperience.slug}</p>
                <p><strong>Category:</strong> {selectedExperience.category ?
                  (selectedExperience.category === 'water-activities' ? 'Water Activities' :
                   selectedExperience.category === 'wellness' ? 'Wellness' :
                   selectedExperience.category === 'excursions' ? 'Excursions' :
                   selectedExperience.category === 'culinary' ? 'Culinary' :
                   selectedExperience.category === 'beach' ? 'Beach Activities' :
                   selectedExperience.category === 'fishing' ? 'Fishing' :
                   selectedExperience.category) : 'Not specified'}
                </p>
                <p><strong>Short Description:</strong> {selectedExperience.description}</p>
                <p><strong>Long Description:</strong> {selectedExperience.longDescription || 'Not provided'}</p>
                <p><strong>Duration:</strong> {selectedExperience.duration}</p>
                <p><strong>Price:</strong> ${selectedExperience.price}</p>
                <p><strong>Group Size:</strong> {selectedExperience.groupSize || 'Not specified'}</p>
                <p><strong>Difficulty:</strong> {selectedExperience.difficulty || 'Not specified'}</p>
                <p><strong>Featured:</strong> {selectedExperience.featured ? 'Yes' : 'No'}</p>
                <p><strong>Created At:</strong> {selectedExperience.createdAt}</p>

                {selectedExperience.features && (
                  <>
                    <h3 style={{ marginTop: '20px' }}>What's Included</h3>
                    <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                      {typeof selectedExperience.features === 'string'
                        ? selectedExperience.features.split('\n').map((feature, index) => (
                            feature.trim() && <Badge key={index} type="default">{feature.trim()}</Badge>
                          ))
                        : Array.isArray(selectedExperience.features) && selectedExperience.features.map((feature, index) => (
                            <Badge key={index} type="default">{feature}</Badge>
                          ))
                      }
                    </div>
                  </>
                )}

                {selectedExperience.itinerary && (
                  <>
                    <h3 style={{ marginTop: '20px' }}>Itinerary</h3>
                    <div style={{ marginTop: '10px' }}>
                      {typeof selectedExperience.itinerary === 'string'
                        ? selectedExperience.itinerary.split('\n').map((item, index) => {
                            if (!item.trim()) return null;
                            const parts = item.split('|').map(part => part.trim());
                            return (
                              <div key={index} style={{ marginBottom: '15px' }}>
                                {parts.length >= 2 && (
                                  <>
                                    <div style={{ display: 'flex', alignItems: 'center', marginBottom: '5px' }}>
                                      <Badge type="primary" style={{ marginRight: '10px' }}>{parts[0]}</Badge>
                                      <strong>{parts[1]}</strong>
                                    </div>
                                    {parts[2] && <p>{parts[2]}</p>}
                                  </>
                                )}
                              </div>
                            );
                          })
                        : Array.isArray(selectedExperience.itinerary) && selectedExperience.itinerary.map((item, index) => (
                            <div key={index} style={{ marginBottom: '15px' }}>
                              <div style={{ display: 'flex', alignItems: 'center', marginBottom: '5px' }}>
                                <Badge type="primary" style={{ marginRight: '10px' }}>{item.time}</Badge>
                                <strong>{item.title}</strong>
                              </div>
                              {item.description && <p>{item.description}</p>}
                            </div>
                          ))
                      }
                    </div>
                  </>
                )}
              </div>

              <div>
                <h3>Main Image</h3>
                <img
                  src={selectedExperience.imageUrl}
                  alt={selectedExperience.title}
                  style={{ width: '100%', borderRadius: '8px', marginBottom: '20px' }}
                />

                {selectedExperience.gallery && (
                  <>
                    <h3>Gallery Images</h3>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '10px' }}>
                      {typeof selectedExperience.gallery === 'string'
                        ? selectedExperience.gallery.split('\n').map((image, index) => (
                            image.trim() && (
                              <img
                                key={index}
                                src={image.trim()}
                                alt={`Gallery ${index + 1}`}
                                style={{ width: '100%', height: '120px', objectFit: 'cover', borderRadius: '4px' }}
                              />
                            )
                          ))
                        : Array.isArray(selectedExperience.gallery) && selectedExperience.gallery.map((image, index) => (
                            <img
                              key={index}
                              src={image}
                              alt={`Gallery ${index + 1}`}
                              style={{ width: '100%', height: '120px', objectFit: 'cover', borderRadius: '4px' }}
                            />
                          ))
                      }
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Experience Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Experience"
        onConfirm={handleDeleteConfirm}
        confirmLabel="Delete"
        confirmVariant="danger"
        loading={loading}
      >
        {selectedExperience && (
          <div>
            <p>Are you sure you want to delete the experience "{selectedExperience.title}"?</p>
            <p>This action cannot be undone.</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default ExperiencesPage;
