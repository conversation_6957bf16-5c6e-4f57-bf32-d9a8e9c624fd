import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faEdit, faTrash, faEye, faUserShield, faUser } from '@fortawesome/free-solid-svg-icons';
import PageHeader from '../../components/dashboard/PageHeader';
import DataTable from '../../components/dashboard/DataTable';
import Modal from '../../components/dashboard/Modal';
import FormBuilder from '../../components/dashboard/FormBuilder';
import { userService, authService } from '../../services';

const RoleTag = styled.span`
  display: inline-block;
  padding: 5px 10px;
  border-radius: 50px;
  font-size: 12px;
  font-weight: 600;
  background-color: ${({ role }) =>
    role === 'admin' ? '#ff9800' :
    role === 'editor' ? '#4caf50' : '#2196f3'};
  color: white;
`;

const RoleIcon = styled.span`
  margin-right: 5px;
`;

const UsersPage = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    username: '',
    email: '',
    role: 'editor',
    password: '',
    confirmPassword: '',
    active: true
  });
  const [formErrors, setFormErrors] = useState({});

  // Current logged in user
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');

  useEffect(() => {
    // Fetch users from API
    const fetchUsers = async () => {
      try {
        setLoading(true);
        const data = await userService.getAllUsers();

        // Map API data to component format
        const mappedUsers = data.map(user => ({
          id: user.id,
          name: user.name,
          username: user.username,
          email: user.email,
          role: user.role,
          active: true, // API doesn't have this field yet
          lastLogin: 'Not available', // API doesn't have this field yet
          createdAt: user.createdAt
        }));

        setUsers(mappedUsers);
      } catch (error) {
        console.error('Error fetching users:', error);
        // Show error message to user
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, []);

  const columns = [
    {
      key: 'name',
      label: 'Name',
      sortable: true
    },
    {
      key: 'username',
      label: 'Username',
      sortable: true
    },
    {
      key: 'email',
      label: 'Email',
      sortable: true
    },
    {
      key: 'role',
      label: 'Role',
      sortable: true,
      render: (row) => (
        <RoleTag role={row.role}>
          <RoleIcon>
            <FontAwesomeIcon icon={row.role === 'admin' ? faUserShield : faUser} />
          </RoleIcon>
          {row.role === 'admin' ? 'Administrator' : 'Editor'}
        </RoleTag>
      )
    },
    {
      key: 'active',
      label: 'Status',
      sortable: true,
      render: (row) => (
        <span style={{ color: row.active ? '#4caf50' : '#f44336' }}>
          {row.active ? 'Active' : 'Inactive'}
        </span>
      )
    },
    {
      key: 'lastLogin',
      label: 'Last Login',
      sortable: true
    },
    {
      key: 'createdAt',
      label: 'Created',
      sortable: true
    }
  ];

  const formFields = [
    {
      name: 'name',
      label: 'Full Name',
      type: 'text',
      placeholder: 'Enter full name',
      required: true
    },
    {
      name: 'username',
      label: 'Username',
      type: 'text',
      placeholder: 'Enter username',
      required: true
    },
    {
      name: 'email',
      label: 'Email',
      type: 'email',
      placeholder: 'Enter email address',
      required: true
    },
    {
      name: 'role',
      label: 'Role',
      type: 'select',
      options: [
        { value: 'admin', label: 'Administrator' },
        { value: 'editor', label: 'Editor' }
      ],
      required: true
    },
    {
      name: 'password',
      label: 'Password',
      type: 'password',
      placeholder: 'Enter password',
      required: true,
      condition: !isEditModalOpen
    },
    {
      name: 'confirmPassword',
      label: 'Confirm Password',
      type: 'password',
      placeholder: 'Confirm password',
      required: true,
      condition: !isEditModalOpen
    },
    {
      name: 'active',
      label: 'Status',
      type: 'switch',
      options: [
        { value: true, label: 'Active' },
        { value: false, label: 'Inactive' }
      ]
    }
  ];

  const handleAddUser = () => {
    setFormData({
      name: '',
      username: '',
      email: '',
      role: 'editor',
      password: '',
      confirmPassword: '',
      active: true
    });
    setFormErrors({});
    setIsAddModalOpen(true);
  };

  const handleViewUser = (user) => {
    setSelectedUser(user);
    setIsViewModalOpen(true);
  };

  const handleEditUser = (user) => {
    setSelectedUser(user);
    setFormData({
      name: user.name,
      username: user.username,
      email: user.email,
      role: user.role,
      active: user.active
    });
    setFormErrors({});
    setIsEditModalOpen(true);
  };

  const handleDeleteUser = async (user) => {
    // Prevent deleting yourself
    if (user.id === currentUser.id) {
      alert('You cannot delete your own account.');
      return;
    }

    if (window.confirm(`Are you sure you want to delete ${user.name}?`)) {
      try {
        setLoading(true);

        // Delete user via API
        await userService.deleteUser(user.id);

        // Update users list
        setUsers(users.filter(u => u.id !== user.id));
      } catch (error) {
        console.error('Error deleting user:', error);
        alert('Failed to delete user. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  const handleAddSubmit = async () => {
    // Validate form
    const errors = {};

    if (!formData.name) errors.name = 'Name is required';
    if (!formData.username) errors.username = 'Username is required';
    if (!formData.email) errors.email = 'Email is required';
    if (!formData.password) errors.password = 'Password is required';
    if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = 'Passwords do not match';
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      // Create user via API
      const userData = {
        name: formData.name,
        username: formData.username,
        email: formData.email,
        password: formData.password,
        role: formData.role
      };

      const newUser = await userService.createUser(userData);

      // Map API response to component format
      const mappedUser = {
        ...newUser,
        active: true,
        lastLogin: 'Never'
      };

      setUsers([...users, mappedUser]);
      setIsAddModalOpen(false);
    } catch (error) {
      console.error('Error creating user:', error);
      alert('Failed to create user. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleEditSubmit = async () => {
    // Validate form
    const errors = {};

    if (!formData.name) errors.name = 'Name is required';
    if (!formData.username) errors.username = 'Username is required';
    if (!formData.email) errors.email = 'Email is required';

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      // Update user via API
      const userData = {
        name: formData.name,
        username: formData.username,
        email: formData.email,
        role: formData.role,
        active: formData.active
      };

      const updatedUser = await userService.updateUser(selectedUser.id, userData);

      // Map API response to component format
      const mappedUser = {
        ...updatedUser,
        active: formData.active,
        lastLogin: selectedUser.lastLogin
      };

      const updatedUsers = users.map(user =>
        user.id === selectedUser.id ? { ...user, ...mappedUser } : user
      );

      setUsers(updatedUsers);
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('Error updating user:', error);
      alert('Failed to update user. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <PageHeader
        title="User Management"
        subtitle="Manage administrators and editors"
        onAdd={handleAddUser}
      />

      <DataTable
        title="Users List"
        columns={columns}
        data={users}
        onView={handleViewUser}
        onEdit={handleEditUser}
        onDelete={handleDeleteUser}
        onAdd={handleAddUser}
        loading={loading}
      />

      {/* Add User Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add User"
        size="medium"
        hideFooter
      >
        <FormBuilder
          title="User Information"
          fields={formFields.filter(field => !field.condition || field.condition)}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleAddSubmit}
          onCancel={() => setIsAddModalOpen(false)}
          submitLabel="Add User"
          loading={loading}
        />
      </Modal>

      {/* Edit User Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit User"
        size="medium"
        hideFooter
      >
        <FormBuilder
          title="User Information"
          fields={formFields.filter(field => !field.condition || field.condition)}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditModalOpen(false)}
          submitLabel="Update User"
          loading={loading}
        />
      </Modal>

      {/* View User Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        title="User Details"
        size="medium"
      >
        {selectedUser && (
          <div>
            <p><strong>Name:</strong> {selectedUser.name}</p>
            <p><strong>Username:</strong> {selectedUser.username}</p>
            <p><strong>Email:</strong> {selectedUser.email}</p>
            <p><strong>Role:</strong> {selectedUser.role === 'admin' ? 'Administrator' : 'Editor'}</p>
            <p><strong>Status:</strong> {selectedUser.active ? 'Active' : 'Inactive'}</p>
            <p><strong>Last Login:</strong> {selectedUser.lastLogin}</p>
            <p><strong>Created:</strong> {selectedUser.createdAt}</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default UsersPage;
