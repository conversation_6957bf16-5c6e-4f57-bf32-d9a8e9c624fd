import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck, faTimes } from '@fortawesome/free-solid-svg-icons';

import PageHeader from '../../components/dashboard/PageHeader';
import DataTable from '../../components/dashboard/DataTable';
import Modal from '../../components/dashboard/Modal';
import FormBuilder from '../../components/dashboard/FormBuilder';
import { destinationService, mediaService } from '../../services';

const Badge = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
  background-color: ${({ type }) => {
    switch (type) {
      case 'success':
        return '#e8f5e9';
      case 'error':
        return '#ffebee';
      default:
        return '#e3f2fd';
    }
  }};
  color: ${({ type }) => {
    switch (type) {
      case 'success':
        return '#388e3c';
      case 'error':
        return '#d32f2f';
      default:
        return '#1976d2';
    }
  }};
`;

const ImagePreview = styled.img`
  width: 60px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
`;

const DestinationsPage = () => {
  // State for destinations data
  const [destinations, setDestinations] = useState([]);
  const [loading, setLoading] = useState(true);

  // State for modals
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);

  // State for form data
  const [formData, setFormData] = useState({});
  const [formErrors, setFormErrors] = useState({});
  const [selectedDestination, setSelectedDestination] = useState(null);

  // Fetch destinations from API
  useEffect(() => {
    const fetchDestinations = async () => {
      try {
        setLoading(true);
        const data = await destinationService.getAllDestinations();
        setDestinations(data);
      } catch (error) {
        console.error('Error fetching destinations:', error);
        // Show error message to user
      } finally {
        setLoading(false);
      }
    };

    fetchDestinations();
  }, []);

  // Table columns
  const columns = [
    {
      key: 'id',
      label: 'ID',
      sortable: true
    },
    {
      key: 'imageUrl',
      label: 'Image',
      sortable: false,
      render: (item) => <ImagePreview src={item.imageUrl} alt={item.title} />
    },
    {
      key: 'title',
      label: 'Title',
      sortable: true
    },
    {
      key: 'slug',
      label: 'Slug',
      sortable: true
    },
    {
      key: 'featured',
      label: 'Featured',
      sortable: true,
      render: (item) => (
        <Badge type={item.featured ? 'success' : 'error'}>
          {item.featured ? (
            <>
              <FontAwesomeIcon icon={faCheck} style={{ marginRight: '5px' }} />
              Yes
            </>
          ) : (
            <>
              <FontAwesomeIcon icon={faTimes} style={{ marginRight: '5px' }} />
              No
            </>
          )}
        </Badge>
      )
    },
    {
      key: 'createdAt',
      label: 'Created At',
      sortable: true
    }
  ];

  // Form fields
  const formFields = [
    {
      type: 'text',
      name: 'title',
      label: 'Title',
      placeholder: 'Enter destination title',
      required: true
    },
    {
      type: 'text',
      name: 'slug',
      label: 'Slug',
      placeholder: 'Enter destination slug',
      required: true
    },
    {
      type: 'select',
      name: 'category',
      label: 'Category',
      options: [
        { value: 'resort', label: 'Resort' },
        { value: 'private-island', label: 'Private Island' },
        { value: 'hotel', label: 'Hotel' },
        { value: 'guesthouse', label: 'Guesthouse' }
      ],
      required: true
    },
    {
      type: 'textarea',
      name: 'description',
      label: 'Short Description',
      placeholder: 'Enter a brief description (shown on cards)',
      required: true
    },
    {
      type: 'textarea',
      name: 'longDescription',
      label: 'Long Description',
      placeholder: 'Enter detailed description for the detail page',
      required: true
    },
    {
      type: 'image',
      name: 'imageUrl',
      label: 'Main Image',
      required: true
    },
    {
      type: 'textarea',
      name: 'features',
      label: 'Features',
      placeholder: 'Enter features, one per line (e.g. Overwater Villas, Private Pools, etc.)',
      required: true
    },
    {
      type: 'textarea',
      name: 'gallery',
      label: 'Gallery Images',
      placeholder: 'Enter image URLs, one per line',
      required: false
    },
    {
      type: 'text',
      name: 'location',
      label: 'Location',
      placeholder: 'Enter location (e.g. Noonu Atoll, Maldives)',
      required: false
    },
    {
      type: 'checkbox',
      name: 'featured',
      label: 'Featured Destination'
    }
  ];

  // Handle add destination
  const handleAddDestination = () => {
    setFormData({});
    setFormErrors({});
    setIsAddModalOpen(true);
  };

  // Handle edit destination
  const handleEditDestination = (destination) => {
    console.log('Editing destination:', destination);
    setSelectedDestination(destination);
    setFormData({ ...destination });
    setFormErrors({});
    setIsEditModalOpen(true);
  };

  // Handle view destination
  const handleViewDestination = (destination) => {
    setSelectedDestination(destination);
    setIsViewModalOpen(true);
  };

  // Handle delete destination
  const handleDeleteDestination = (destination) => {
    setSelectedDestination(destination);
    setIsDeleteModalOpen(true);
  };

  // Handle form submit for add
  const handleAddSubmit = async () => {
    // Validate form
    const errors = {};
    formFields.forEach((field) => {
      if (field.required && !formData[field.name]) {
        errors[field.name] = `${field.label} is required`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);

      // Handle image upload if it's a File object
      if (formData.imageFile && formData.imageFile instanceof File) {
        const formDataObj = new FormData();
        formDataObj.append('file', formData.imageFile);
        formDataObj.append('title', formData.title);
        formDataObj.append('alt', formData.title);

        const uploadResult = await mediaService.uploadMedia(formDataObj);
        formData.imageUrl = uploadResult.url;
      }

      // Create destination
      const newDestination = await destinationService.createDestination(formData);

      // Update destinations list
      setDestinations([...destinations, newDestination]);
      setIsAddModalOpen(false);
    } catch (error) {
      console.error('Error creating destination:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  // Handle form submit for edit
  const handleEditSubmit = async () => {
    // Validate form
    const errors = {};
    formFields.forEach((field) => {
      if (field.required && !formData[field.name]) {
        errors[field.name] = `${field.label} is required`;
      }
    });

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    try {
      setLoading(true);
      console.log('Submitting edit for destination:', selectedDestination.id);
      console.log('Form data:', formData);

      // Handle image upload if it's a File object
      if (formData.imageFile && formData.imageFile instanceof File) {
        console.log('Uploading new image file');
        const formDataObj = new FormData();
        formDataObj.append('file', formData.imageFile);
        formDataObj.append('title', formData.title);
        formDataObj.append('alt', formData.title);

        const uploadResult = await mediaService.uploadMedia(formDataObj);
        console.log('Image upload result:', uploadResult);
        formData.imageUrl = uploadResult.url;
      }

      // Update destination
      console.log('Calling updateDestination with ID:', selectedDestination.id);
      const updatedDestination = await destinationService.updateDestination(selectedDestination.id, formData);
      console.log('Update successful, result:', updatedDestination);

      // Update destinations list
      const updatedDestinations = destinations.map((destination) =>
        destination.id === selectedDestination.id ? updatedDestination : destination
      );

      setDestinations(updatedDestinations);
      setIsEditModalOpen(false);
    } catch (error) {
      console.error('Error updating destination:', error);
      alert(`Error updating destination: ${error.message || 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  // Handle delete confirm
  const handleDeleteConfirm = async () => {
    try {
      setLoading(true);

      // Delete destination
      await destinationService.deleteDestination(selectedDestination.id);

      // Update destinations list
      const updatedDestinations = destinations.filter(
        (destination) => destination.id !== selectedDestination.id
      );

      setDestinations(updatedDestinations);
      setIsDeleteModalOpen(false);
    } catch (error) {
      console.error('Error deleting destination:', error);
      // Show error message to user
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <PageHeader
        title="Destinations"
        subtitle="Manage your destination listings"
        onAdd={handleAddDestination}
      />

      <DataTable
        title="Destinations List"
        columns={columns}
        data={destinations}
        onView={handleViewDestination}
        onEdit={handleEditDestination}
        onDelete={handleDeleteDestination}
        onAdd={handleAddDestination}
      />

      {/* Add Destination Modal */}
      <Modal
        isOpen={isAddModalOpen}
        onClose={() => setIsAddModalOpen(false)}
        title="Add Destination"
        size="large"
        hideFooter
      >
        <FormBuilder
          title="Destination Information"
          fields={formFields}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleAddSubmit}
          onCancel={() => setIsAddModalOpen(false)}
          submitLabel="Add Destination"
          loading={loading}
        />
      </Modal>

      {/* Edit Destination Modal */}
      <Modal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Destination"
        size="large"
        hideFooter
      >
        <FormBuilder
          title="Destination Information"
          fields={formFields}
          values={formData}
          errors={formErrors}
          onChange={setFormData}
          onSubmit={handleEditSubmit}
          onCancel={() => setIsEditModalOpen(false)}
          submitLabel="Update Destination"
          loading={loading}
        />
      </Modal>

      {/* View Destination Modal */}
      <Modal
        isOpen={isViewModalOpen}
        onClose={() => setIsViewModalOpen(false)}
        title="Destination Details"
        size="large"
      >
        {selectedDestination && (
          <div>
            <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '20px' }}>
              <div>
                <h2>{selectedDestination.title}</h2>
                <p><strong>Slug:</strong> {selectedDestination.slug}</p>
                <p><strong>Category:</strong> {selectedDestination.category ?
                  (selectedDestination.category === 'resort' ? 'Resort' :
                   selectedDestination.category === 'private-island' ? 'Private Island' :
                   selectedDestination.category === 'hotel' ? 'Hotel' :
                   selectedDestination.category === 'guesthouse' ? 'Guesthouse' :
                   selectedDestination.category) : 'Not specified'}
                </p>
                <p><strong>Short Description:</strong> {selectedDestination.description}</p>
                <p><strong>Long Description:</strong> {selectedDestination.longDescription || 'Not provided'}</p>
                <p><strong>Location:</strong> {selectedDestination.location || 'Not provided'}</p>
                <p><strong>Featured:</strong> {selectedDestination.featured ? 'Yes' : 'No'}</p>
                <p><strong>Created At:</strong> {selectedDestination.createdAt}</p>

                <h3 style={{ marginTop: '20px' }}>Features</h3>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                  {selectedDestination.features ? (
                    typeof selectedDestination.features === 'string'
                      ? selectedDestination.features.split('\n').map((feature, index) => (
                          <Badge key={index} type="default">{feature.trim()}</Badge>
                        ))
                      : selectedDestination.features.map((feature, index) => (
                          <Badge key={index} type="default">{feature}</Badge>
                        ))
                  ) : (
                    <p>No features provided</p>
                  )}
                </div>
              </div>

              <div>
                <h3>Main Image</h3>
                <img
                  src={selectedDestination.imageUrl}
                  alt={selectedDestination.title}
                  style={{ width: '100%', borderRadius: '8px', marginBottom: '20px' }}
                />

                {selectedDestination.gallery && (
                  <>
                    <h3>Gallery Images</h3>
                    <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '10px' }}>
                      {typeof selectedDestination.gallery === 'string'
                        ? selectedDestination.gallery.split('\n').map((image, index) => (
                            image.trim() && (
                              <img
                                key={index}
                                src={image.trim()}
                                alt={`Gallery ${index + 1}`}
                                style={{ width: '100%', height: '120px', objectFit: 'cover', borderRadius: '4px' }}
                              />
                            )
                          ))
                        : selectedDestination.gallery.map((image, index) => (
                            <img
                              key={index}
                              src={image}
                              alt={`Gallery ${index + 1}`}
                              style={{ width: '100%', height: '120px', objectFit: 'cover', borderRadius: '4px' }}
                            />
                          ))
                      }
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* Delete Destination Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Delete Destination"
        onConfirm={handleDeleteConfirm}
        confirmLabel="Delete"
        confirmVariant="danger"
        loading={loading}
      >
        {selectedDestination && (
          <div>
            <p>Are you sure you want to delete the destination "{selectedDestination.title}"?</p>
            <p>This action cannot be undone.</p>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DestinationsPage;
