import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faUser, faLock, faExclamationCircle } from '@fortawesome/free-solid-svg-icons';
import { useAuth } from '../../context/AuthContext';

const LoginContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fb;
`;

const LoginCard = styled.div`
  width: 100%;
  max-width: 400px;
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  padding: 30px;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 30px;

  h1 {
    color: #0099b8;
    margin: 0;
    font-size: 2rem;
  }

  p {
    color: #666;
    margin: 5px 0 0;
  }
`;

const Form = styled.form`
  display: flex;
  flex-direction: column;
  gap: 20px;
`;

const FormGroup = styled.div`
  position: relative;
`;

const Input = styled.input`
  width: 100%;
  padding: 12px 15px 12px 40px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;

  &:focus {
    outline: none;
    border-color: #0099b8;
  }
`;

const InputIcon = styled.span`
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #999;
`;

const SubmitButton = styled.button`
  background-color: #0099b8;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;

  &:hover {
    background-color: #007a94;
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  background-color: #ffebee;
  color: #d32f2f;
  padding: 10px 15px;
  border-radius: 4px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const LoginPage = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [formError, setFormError] = useState('');
  const location = useLocation();

  // Use the auth context
  const { login, loading, error, isAuthenticated } = useAuth();

  // Check for expired session parameter
  useEffect(() => {
    const queryParams = new URLSearchParams(location.search);
    if (queryParams.get('expired') === 'true') {
      setFormError('Your session has expired. Please log in again.');
    }
  }, [location]);

  // If user is already logged in, redirect to dashboard
  if (isAuthenticated()) {
    return <Navigate to="/admin/dashboard" replace />;
  }

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Form validation
    if (!username.trim() || !password.trim()) {
      setFormError('Please enter both username and password');
      return;
    }

    try {
      console.log('Submitting login form with username:', username);
      // Call the login function from auth context
      const result = await login(username, password);
      console.log('Login successful:', result);
    } catch (err) {
      console.error('Login error in component:', err);
      setFormError(err.message || 'Login failed. Please check your credentials and try again.');
    }
  };

  return (
    <LoginContainer>
      <LoginCard>
        <Logo>
          <h1>RuhKandhu</h1>
          <p>Admin Dashboard</p>
        </Logo>

        {formError && (
          <ErrorMessage>
            <FontAwesomeIcon icon={faExclamationCircle} />
            <span>{formError}</span>
          </ErrorMessage>
        )}

        <Form onSubmit={handleSubmit}>
          <FormGroup>
            <InputIcon>
              <FontAwesomeIcon icon={faUser} />
            </InputIcon>
            <Input
              type="text"
              placeholder="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
            />
          </FormGroup>

          <FormGroup>
            <InputIcon>
              <FontAwesomeIcon icon={faLock} />
            </InputIcon>
            <Input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </FormGroup>

          <SubmitButton type="submit" disabled={loading}>
            {loading ? 'Logging in...' : 'Login'}
          </SubmitButton>
        </Form>
      </LoginCard>
    </LoginContainer>
  );
};

export default LoginPage;
