import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';

const EmptyStateContainer = styled.div`
  text-align: center;
  padding: 60px 20px;
  background: #f9f9f9;
  border-radius: 12px;
  margin: 20px 0;
`;

const EmptyIcon = styled.div`
  font-size: 64px;
  color: #ddd;
  margin-bottom: 20px;
`;

const EmptyTitle = styled.h3`
  font-size: 24px;
  color: #333;
  margin-bottom: 12px;
  font-weight: 600;
`;

const EmptyDescription = styled.p`
  font-size: 16px;
  color: #666;
  margin-bottom: 30px;
  line-height: 1.6;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
`;

const EmptyButton = styled(Link)`
  display: inline-block;
  background: #0099b8;
  color: white;
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: background 0.3s ease;

  &:hover {
    background: #007a94;
  }
`;

const EmptyState = ({ 
  icon = "🏝️", 
  title = "No Content Available", 
  description = "There's no content to display at the moment.", 
  actionText = "Go Back",
  actionLink = "/",
  type = "general"
}) => {
  // Customize content based on type
  const getContent = () => {
    switch (type) {
      case 'destinations':
        return {
          icon: "🏝️",
          title: "No Destinations Yet",
          description: "We're working on adding amazing destinations for you to explore. Check back soon!",
          actionText: "Explore Other Pages",
          actionLink: "/"
        };
      case 'experiences':
        return {
          icon: "🤿",
          title: "No Experiences Available",
          description: "We're curating incredible experiences for your next adventure. Stay tuned!",
          actionText: "View Destinations",
          actionLink: "/destinations"
        };
      case 'offers':
        return {
          icon: "🎉",
          title: "No Special Offers",
          description: "We're preparing exciting deals and packages. Check back soon for amazing offers!",
          actionText: "Browse Destinations",
          actionLink: "/destinations"
        };
      case 'admin-destinations':
        return {
          icon: "🏝️",
          title: "No Destinations Created",
          description: "Start building your travel portfolio by adding your first destination.",
          actionText: "Add Destination",
          actionLink: "/admin/destinations/new"
        };
      case 'admin-experiences':
        return {
          icon: "🤿",
          title: "No Experiences Created",
          description: "Create memorable experiences for your customers to enjoy.",
          actionText: "Add Experience",
          actionLink: "/admin/experiences/new"
        };
      case 'admin-offers':
        return {
          icon: "🎉",
          title: "No Offers Created",
          description: "Create special offers and packages to attract more customers.",
          actionText: "Add Offer",
          actionLink: "/admin/offers/new"
        };
      case 'admin-media':
        return {
          icon: "📸",
          title: "No Media Files",
          description: "Upload images and videos to enhance your content.",
          actionText: "Upload Media",
          actionLink: "/admin/media/upload"
        };
      case 'admin-inquiries':
        return {
          icon: "📧",
          title: "No Inquiries Yet",
          description: "Customer inquiries will appear here when they contact you.",
          actionText: "Back to Dashboard",
          actionLink: "/admin"
        };
      default:
        return {
          icon,
          title,
          description,
          actionText,
          actionLink
        };
    }
  };

  const content = getContent();

  return (
    <EmptyStateContainer>
      <EmptyIcon>{content.icon}</EmptyIcon>
      <EmptyTitle>{content.title}</EmptyTitle>
      <EmptyDescription>{content.description}</EmptyDescription>
      <EmptyButton to={content.actionLink}>
        {content.actionText}
      </EmptyButton>
    </EmptyStateContainer>
  );
};

export default EmptyState;
