import React from 'react';
import styled from 'styled-components';
import { useSocket } from '../../contexts/SocketContext';

const NotificationContainer = styled.div`
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
`;

const NotificationItem = styled.div`
  background: ${props => {
    switch (props.type) {
      case 'success': return '#d4edda';
      case 'warning': return '#fff3cd';
      case 'error': return '#f8d7da';
      default: return '#d1ecf1';
    }
  }};
  border: 1px solid ${props => {
    switch (props.type) {
      case 'success': return '#c3e6cb';
      case 'warning': return '#ffeaa7';
      case 'error': return '#f5c6cb';
      default: return '#bee5eb';
    }
  }};
  color: ${props => {
    switch (props.type) {
      case 'success': return '#155724';
      case 'warning': return '#856404';
      case 'error': return '#721c24';
      default: return '#0c5460';
    }
  }};
  padding: 12px 16px;
  margin-bottom: 10px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  animation: slideIn 0.3s ease-out;

  @keyframes slideIn {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
`;

const NotificationContent = styled.div`
  flex: 1;
`;

const NotificationMessage = styled.div`
  font-weight: 500;
  margin-bottom: 4px;
`;

const NotificationTime = styled.div`
  font-size: 12px;
  opacity: 0.7;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: inherit;
  cursor: pointer;
  font-size: 18px;
  line-height: 1;
  padding: 0;
  margin-left: 12px;
  opacity: 0.7;

  &:hover {
    opacity: 1;
  }
`;

const ConnectionStatus = styled.div`
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: ${props => props.connected ? '#28a745' : '#dc3545'};
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
  display: flex;
  align-items: center;
  gap: 6px;

  &::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
    animation: ${props => props.connected ? 'pulse 2s infinite' : 'none'};
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
  }
`;

const ClearAllButton = styled.button`
  background: #6c757d;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
  margin-bottom: 10px;
  width: 100%;

  &:hover {
    background: #5a6268;
  }
`;

const NotificationCenter = () => {
  const { notifications, connected, removeNotification, clearAllNotifications } = useSocket();

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  return (
    <>
      <NotificationContainer>
        {notifications.length > 0 && (
          <ClearAllButton onClick={clearAllNotifications}>
            Clear All ({notifications.length})
          </ClearAllButton>
        )}
        {notifications.map(notification => (
          <NotificationItem key={notification.id} type={notification.type}>
            <NotificationContent>
              <NotificationMessage>{notification.message}</NotificationMessage>
              <NotificationTime>{formatTime(notification.timestamp)}</NotificationTime>
            </NotificationContent>
            <CloseButton onClick={() => removeNotification(notification.id)}>
              ×
            </CloseButton>
          </NotificationItem>
        ))}
      </NotificationContainer>

      <ConnectionStatus connected={connected}>
        {connected ? 'Real-time Connected' : 'Disconnected'}
      </ConnectionStatus>
    </>
  );
};

export default NotificationCenter;
