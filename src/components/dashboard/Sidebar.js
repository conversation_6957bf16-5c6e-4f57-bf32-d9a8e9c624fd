import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faTachometerAlt,
  faMapMarkerAlt,
  faHiking,
  faGift,
  faComment,
  faUsers,
  faImages,
  faUserShield,
  faCog,
  faSignOutAlt,
  faExternalLinkAlt,
  faEnvelope,
  faFileAlt
} from '@fortawesome/free-solid-svg-icons';

const SidebarContainer = styled.div`
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
  width: ${({ isOpen }) => (isOpen ? '250px' : '70px')};
  background-color: #0099b8;
  color: #fff;
  transition: width 0.3s ease;
  z-index: 1000;
  overflow-x: hidden;
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);

  @media (max-width: 768px) {
    transform: ${({ isOpen }) => (isOpen ? 'translateX(0)' : 'translateX(-100%)')};
    width: 250px;
  }
`;

const SidebarHeader = styled.div`
  padding: 20px;
  text-align: ${({ isOpen }) => (isOpen ? 'left' : 'center')};
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);

  h2 {
    margin: 0;
    font-size: ${({ isOpen }) => (isOpen ? '1.5rem' : '1rem')};
    white-space: nowrap;
  }

  p {
    margin: 5px 0 0;
    font-size: 0.8rem;
    opacity: 0.8;
    display: ${({ isOpen }) => (isOpen ? 'block' : 'none')};
  }
`;

const SidebarNav = styled.nav`
  padding: 20px 0;
  overflow-y: auto;
  max-height: calc(100vh - 180px);
`;

const NavList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
`;

const NavItem = styled.li`
  margin-bottom: 5px;
`;

const NavLink = styled(Link)`
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: #fff;
  text-decoration: none;
  transition: background-color 0.3s;
  border-left: 3px solid ${({ active }) => (active ? '#fff' : 'transparent')};
  background-color: ${({ active }) => (active ? 'rgba(255, 255, 255, 0.1)' : 'transparent')};

  &:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }

  svg {
    margin-right: ${({ isOpen }) => (isOpen ? '10px' : '0')};
    font-size: 1.2rem;
  }

  span {
    display: ${({ isOpen }) => (isOpen ? 'inline' : 'none')};
    white-space: nowrap;
  }
`;

const SidebarFooter = styled.div`
  position: absolute;
  bottom: 0;
  width: 100%;
  padding: 15px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  flex-direction: ${({ isOpen }) => (isOpen ? 'row' : 'column')};
  justify-content: ${({ isOpen }) => (isOpen ? 'space-between' : 'center')};
  gap: 10px;

  a {
    color: #fff;
    text-decoration: none;
    display: flex;
    align-items: center;
    font-size: 0.9rem;

    svg {
      margin-right: ${({ isOpen }) => (isOpen ? '8px' : '0')};
    }

    span {
      display: ${({ isOpen }) => (isOpen ? 'inline' : 'none')};
    }

    &:hover {
      text-decoration: underline;
    }
  }
`;

const Sidebar = ({ isOpen }) => {
  const location = useLocation();

  const handleLogout = () => {
    localStorage.removeItem('user');
    localStorage.removeItem('token');
    window.location.href = '/admin/login';
  };

  return (
    <SidebarContainer isOpen={isOpen}>
      <SidebarHeader isOpen={isOpen}>
        <h2>RuhKandhu</h2>
        {isOpen && <p>Admin Panel</p>}
      </SidebarHeader>

      <SidebarNav>
        <NavList>
          <NavItem>
            <NavLink
              to="/admin/dashboard"
              active={location.pathname === '/admin/dashboard' ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faTachometerAlt} />
              {isOpen && <span>Dashboard</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/destinations"
              active={location.pathname.includes('/admin/destinations') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faMapMarkerAlt} />
              {isOpen && <span>Destinations</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/experiences"
              active={location.pathname.includes('/admin/experiences') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faHiking} />
              {isOpen && <span>Experiences</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/offers"
              active={location.pathname.includes('/admin/offers') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faGift} />
              {isOpen && <span>Special Offers</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/testimonials"
              active={location.pathname.includes('/admin/testimonials') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faComment} />
              {isOpen && <span>Testimonials</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/subscribers"
              active={location.pathname.includes('/admin/subscribers') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faUsers} />
              {isOpen && <span>Subscribers</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/inquiries"
              active={location.pathname.includes('/admin/inquiries') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faEnvelope} />
              {isOpen && <span>Inquiries</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/media"
              active={location.pathname.includes('/admin/media') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faImages} />
              {isOpen && <span>Media Library</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/content"
              active={location.pathname.includes('/admin/content') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faFileAlt} />
              {isOpen && <span>Content Management</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/users"
              active={location.pathname.includes('/admin/users') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faUserShield} />
              {isOpen && <span>Users</span>}
            </NavLink>
          </NavItem>
          <NavItem>
            <NavLink
              to="/admin/settings"
              active={location.pathname.includes('/admin/settings') ? 1 : 0}
              isOpen={isOpen}
            >
              <FontAwesomeIcon icon={faCog} />
              {isOpen && <span>Settings</span>}
            </NavLink>
          </NavItem>
        </NavList>
      </SidebarNav>

      <SidebarFooter isOpen={isOpen}>
        <a href="/" target="_blank" rel="noopener noreferrer">
          <FontAwesomeIcon icon={faExternalLinkAlt} />
          {isOpen && <span>View Website</span>}
        </a>
        <a href="#" onClick={handleLogout}>
          <FontAwesomeIcon icon={faSignOutAlt} />
          {isOpen && <span>Logout</span>}
        </a>
      </SidebarFooter>
    </SidebarContainer>
  );
};

export default Sidebar;
