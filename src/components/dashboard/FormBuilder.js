import React from 'react';
import styled from 'styled-components';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faTimes, faSave, faArrowLeft } from '@fortawesome/free-solid-svg-icons';

const FormContainer = styled.div`
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  overflow: hidden;
`;

const FormHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
`;

const FormTitle = styled.h3`
  margin: 0;
  font-size: 1.2rem;
  color: #333;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #999;
  cursor: pointer;
  font-size: 1.2rem;

  &:hover {
    color: #666;
  }
`;

const FormContent = styled.div`
  padding: 20px;
`;

const FormGroup = styled.div`
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
`;

const Input = styled.input`
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;

  &:focus {
    outline: none;
    border-color: #0099b8;
  }
`;

const Textarea = styled.textarea`
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
  min-height: 120px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #0099b8;
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  transition: border-color 0.3s;
  background-color: #fff;

  &:focus {
    outline: none;
    border-color: #0099b8;
  }
`;

const Checkbox = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;

  input {
    margin: 0;
  }
`;

const RadioGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`;

const RadioOption = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;

  input {
    margin: 0;
  }
`;

const ImageUpload = styled.div`
  border: 2px dashed #ddd;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s;

  &:hover {
    border-color: #0099b8;
  }

  input {
    display: none;
  }
`;

const ImagePreview = styled.div`
  margin-top: 15px;

  img {
    max-width: 100%;
    max-height: 200px;
    border-radius: 4px;
  }
`;

const ErrorMessage = styled.div`
  color: #f44336;
  font-size: 0.85rem;
  margin-top: 5px;
`;

const FormFooter = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  padding: 15px 20px;
  border-top: 1px solid #eee;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s;

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const CancelButton = styled(Button)`
  background-color: #f5f5f5;
  color: #666;
  border: 1px solid #ddd;

  &:hover:not(:disabled) {
    background-color: #eee;
  }
`;

const SaveButton = styled(Button)`
  background-color: #0099b8;
  color: white;
  border: 1px solid #0099b8;

  &:hover:not(:disabled) {
    background-color: #007a94;
  }
`;

const FormBuilder = ({
  title,
  fields,
  values,
  errors = {},
  onChange,
  onSubmit,
  onCancel,
  submitLabel = 'Save',
  cancelLabel = 'Cancel',
  loading = false
}) => {
  const handleChange = (name, value) => {
    onChange({ ...values, [name]: value });
  };

  const renderField = (field) => {
    const { type, name, label, placeholder, options, required } = field;

    switch (type) {
      case 'text':
      case 'email':
      case 'password':
      case 'number':
      case 'date':
        return (
          <FormGroup key={name}>
            <Label>{label} {required && <span style={{ color: '#f44336' }}>*</span>}</Label>
            <Input
              type={type}
              name={name}
              value={values[name] || ''}
              placeholder={placeholder}
              onChange={(e) => handleChange(name, e.target.value)}
              required={required}
            />
            {errors[name] && <ErrorMessage>{errors[name]}</ErrorMessage>}
          </FormGroup>
        );

      case 'textarea':
        return (
          <FormGroup key={name}>
            <Label>{label} {required && <span style={{ color: '#f44336' }}>*</span>}</Label>
            <Textarea
              name={name}
              value={values[name] || ''}
              placeholder={placeholder}
              onChange={(e) => handleChange(name, e.target.value)}
              required={required}
            />
            {errors[name] && <ErrorMessage>{errors[name]}</ErrorMessage>}
          </FormGroup>
        );

      case 'select':
        return (
          <FormGroup key={name}>
            <Label>{label} {required && <span style={{ color: '#f44336' }}>*</span>}</Label>
            <Select
              name={name}
              value={values[name] || ''}
              onChange={(e) => handleChange(name, e.target.value)}
              required={required}
            >
              <option value="">{placeholder || 'Select an option'}</option>
              {options.map((option) => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </Select>
            {errors[name] && <ErrorMessage>{errors[name]}</ErrorMessage>}
          </FormGroup>
        );

      case 'checkbox':
        return (
          <FormGroup key={name}>
            <Checkbox>
              <input
                type="checkbox"
                name={name}
                checked={values[name] || false}
                onChange={(e) => handleChange(name, e.target.checked)}
              />
              <Label style={{ margin: 0 }}>{label}</Label>
            </Checkbox>
            {errors[name] && <ErrorMessage>{errors[name]}</ErrorMessage>}
          </FormGroup>
        );

      case 'radio':
        return (
          <FormGroup key={name}>
            <Label>{label} {required && <span style={{ color: '#f44336' }}>*</span>}</Label>
            <RadioGroup>
              {options.map((option) => (
                <RadioOption key={option.value}>
                  <input
                    type="radio"
                    name={name}
                    value={option.value}
                    checked={values[name] === option.value}
                    onChange={(e) => handleChange(name, e.target.value)}
                    required={required}
                  />
                  <span>{option.label}</span>
                </RadioOption>
              ))}
            </RadioGroup>
            {errors[name] && <ErrorMessage>{errors[name]}</ErrorMessage>}
          </FormGroup>
        );

      case 'image':
        return (
          <FormGroup key={name}>
            <Label>{label} {required && <span style={{ color: '#f44336' }}>*</span>}</Label>
            <ImageUpload onClick={() => document.getElementById(`${name}-upload`).click()}>
              <input
                id={`${name}-upload`}
                type="file"
                accept="image/*"
                onChange={(e) => {
                  const file = e.target.files[0];
                  if (file) {
                    // Store the actual file object for API upload
                    handleChange('imageFile', file);

                    // Also create a preview URL
                    const reader = new FileReader();
                    reader.onloadend = () => {
                      handleChange(name, reader.result);
                    };
                    reader.readAsDataURL(file);
                  }
                }}
              />
              <p>Click to upload an image</p>
              {values[name] && (
                <ImagePreview>
                  <img src={values[name]} alt="Preview" />
                </ImagePreview>
              )}
            </ImageUpload>
            {errors[name] && <ErrorMessage>{errors[name]}</ErrorMessage>}
          </FormGroup>
        );

      default:
        return null;
    }
  };

  return (
    <FormContainer>
      <FormHeader>
        <FormTitle>{title}</FormTitle>
        {onCancel && (
          <CloseButton onClick={onCancel}>
            <FontAwesomeIcon icon={faTimes} />
          </CloseButton>
        )}
      </FormHeader>

      <FormContent>
        {fields.map(renderField)}
      </FormContent>

      <FormFooter>
        {onCancel && (
          <CancelButton onClick={onCancel}>
            <FontAwesomeIcon icon={faArrowLeft} />
            {cancelLabel}
          </CancelButton>
        )}
        <SaveButton onClick={onSubmit} disabled={loading}>
          <FontAwesomeIcon icon={faSave} />
          {loading ? 'Saving...' : submitLabel}
        </SaveButton>
      </FormFooter>
    </FormContainer>
  );
};

export default FormBuilder;
