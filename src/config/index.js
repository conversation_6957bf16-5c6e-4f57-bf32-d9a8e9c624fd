const config = {
  apiUrl: process.env.REACT_APP_API_URL || 'http://localhost:5000',
  cdnUrl: process.env.REACT_APP_CDN_URL || '',
  socketUrl: process.env.REACT_APP_SOCKET_URL || 'ws://localhost:5000',
  environment: process.env.REACT_APP_ENV || 'development',
  version: process.env.REACT_APP_VERSION || '1.0.0',
  siteName: process.env.REACT_APP_SITE_NAME || 'Lux Voyage',
  siteDescription: process.env.REACT_APP_SITE_DESCRIPTION || 'Luxury travel experiences in the Maldives',
  googleAnalyticsId: process.env.REACT_APP_GOOGLE_ANALYTICS_ID,
  sentryDsn: process.env.REACT_APP_SENTRY_DSN,
  
  // Feature flags
  features: {
    enableAnalytics: process.env.REACT_APP_ENV === 'production',
    enableErrorTracking: process.env.REACT_APP_ENV === 'production',
    enableCDN: process.env.REACT_APP_ENV === 'production'
  },

  // API endpoints
  endpoints: {
    auth: '/api/auth',
    users: '/api/users',
    destinations: '/api/destinations',
    experiences: '/api/experiences',
    offers: '/api/offers',
    inquiries: '/api/inquiries',
    media: '/api/media',
    settings: '/api/settings'
  },

  // CDN paths
  cdnPaths: {
    images: '/images',
    uploads: '/uploads',
    assets: '/assets'
  }
};

export default config; 