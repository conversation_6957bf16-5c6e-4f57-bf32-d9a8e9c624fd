import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { createGlobalStyle } from 'styled-components';

// Frontend Layout and Pages
import Layout from './components/layout/Layout';
import HomePage from './pages/HomePage';
import DestinationsPage from './pages/DestinationsPage';
import DestinationDetailPage from './pages/DestinationDetailPage';
import ExperiencesPage from './pages/ExperiencesPage';
import ExperienceDetailPage from './pages/ExperienceDetailPage';
import SpecialOffersPage from './pages/SpecialOffersPage';
import OfferDetailPage from './pages/OfferDetailPage';
import DiscoverPage from './pages/DiscoverPage';
import AboutPage from './pages/AboutPage';
import ContactPage from './pages/ContactPage';
import BookingInquiryPage from './pages/BookingInquiryPage';
import SonevaJaniDemo from './pages/SonevaJaniDemo';
import DetailPagesTestPage from './pages/DetailPagesTestPage';
import NotFoundPage from './pages/NotFoundPage';

// Admin Dashboard
import DashboardLayout from './components/dashboard/DashboardLayout';
import ProtectedRoute from './components/auth/ProtectedRoute';
import LoginPage from './pages/admin/LoginPage';
import DashboardPage from './pages/admin/DashboardPage';
import { default as AdminDestinationsPage } from './pages/admin/DestinationsPage';
import { default as AdminExperiencesPage } from './pages/admin/ExperiencesPage';
import { default as AdminOffersPage } from './pages/admin/OffersPage';
import UsersPage from './pages/admin/UsersPage';
import InquiriesPage from './pages/admin/InquiriesPage';
import MediaLibraryPage from './pages/admin/MediaLibraryPage';
import SettingsPage from './pages/admin/SettingsPage';
import ContentManagementPage from './pages/admin/ContentManagementPage';

// Context Providers
import { AuthProvider } from './context/AuthContext';
import { SocketProvider } from './contexts/SocketContext';

const GlobalStyle = createGlobalStyle`
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    background-color: #f9f9f9;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Cormorant Garamond', serif;
    font-weight: 600;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  a {
    text-decoration: none;
    color: #0099b8;
  }

  .text-center {
    text-align: center;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }

  section {
    padding: 80px 0;
  }
`;

function App() {
  return (
    <AuthProvider>
      <SocketProvider>
        <Router>
          <GlobalStyle />
          <Routes>
          {/* Frontend Routes */}
          <Route path="/" element={<Layout />}>
            <Route index element={<HomePage />} />
            <Route path="destinations" element={<DestinationsPage />} />
            <Route path="destinations/:slug" element={<DestinationDetailPage />} />
            <Route path="experiences" element={<ExperiencesPage />} />
            <Route path="experiences/:slug" element={<ExperienceDetailPage />} />
            <Route path="special-offers" element={<SpecialOffersPage />} />
            <Route path="special-offers/:slug" element={<OfferDetailPage />} />
            <Route path="discover" element={<DiscoverPage />} />
            <Route path="about" element={<AboutPage />} />
            <Route path="contact" element={<ContactPage />} />
            <Route path="booking-inquiry" element={<BookingInquiryPage />} />
            <Route path="soneva-jani-demo" element={<SonevaJaniDemo />} />
            <Route path="detail-pages-test" element={<DetailPagesTestPage />} />
          </Route>

          {/* Admin Routes */}
          <Route path="/admin/login" element={<LoginPage />} />

          <Route path="/admin" element={<ProtectedRoute />}>
            <Route element={<DashboardLayout />}>
              <Route path="dashboard" element={<DashboardPage />} />
              <Route path="destinations" element={<AdminDestinationsPage />} />
              <Route path="experiences" element={<AdminExperiencesPage />} />
              <Route path="offers" element={<AdminOffersPage />} />
              <Route path="users" element={<UsersPage />} />
              <Route path="inquiries" element={<InquiriesPage />} />
              <Route path="media" element={<MediaLibraryPage />} />
              <Route path="content" element={<ContentManagementPage />} />
              <Route path="settings" element={<SettingsPage />} />
            </Route>
          </Route>

          {/* 404 Route */}
          <Route path="*" element={<NotFoundPage />} />
        </Routes>
        </Router>
      </SocketProvider>
    </AuthProvider>
  );
}

export default App;
