# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules/
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build
/dist

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for uploaded files
/uploads/*
!/uploads/.gitkeep

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Database
*.sqlite
*.sqlite3
*.db

# Temporary files
*.tmp
*.temp
.cache/

# System Files
.DS_Store
Thumbs.db
