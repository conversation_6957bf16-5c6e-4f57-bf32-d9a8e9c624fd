# Lux Voyage Travel Agency

A modern travel agency platform built with React and Node.js, featuring real-time updates, secure authentication, and a comprehensive booking system.

## 🚀 Quick Start (Clean Installation)

**The application has been cleaned of dummy data and is ready for real content!**

### Prerequisites
- Node.js (v14 or higher)
- npm or yarn

### Installation & Setup

1. **Clone and Install**
   ```bash
   git clone <repository-url>
   cd RuhKandhu
   npm install
   ```

2. **Start with Clean Data**
   ```bash
   # Option 1: Start both servers manually
   npm run server    # Backend (port 5000)
   npm start         # Frontend (port 3000)

   # Option 2: Use the clean restart script
   npm run restart:clean
   ```

3. **Access the Application**
   - **Frontend**: http://localhost:3000
   - **Admin Dashboard**: http://localhost:3000/admin
   - **Backend API**: http://localhost:5000

4. **Default Admin Login**
   - Username: `admin`
   - Password: `admin123`

## ✨ What's New - Clean Data Ready

### 🧹 Removed Dummy Data
- ✅ All dummy destinations, experiences, and offers removed
- ✅ Database cleared and optimized
- ✅ Frontend components now use real API calls
- ✅ Empty states added for when no content exists
- ✅ Ready for production data

### 🔧 Fixes Applied
- ✅ Real-time API integration for all content
- ✅ Proper error handling for empty data
- ✅ Clean database initialization
- ✅ Production-ready settings
- ✅ Improved user experience with empty states

### 📝 New Scripts Available
- `npm run db:clear` - Clear dummy data from database
- `npm run restart:clean` - Restart application with clean setup

## Project Structure

```
.
├── build/              # Production build files
├── config/            # Configuration files
│   ├── default.js     # Default configuration
│   ├── production.js  # Production configuration
│   └── index.js       # Configuration loader
├── database/          # Database migrations and seeds
├── public/           # Static files
├── scripts/          # Deployment and utility scripts
│   ├── deploy.js     # Deployment script
│   └── rollback.js   # Rollback script
├── src/              # Source code
│   ├── assets/       # Static assets
│   ├── components/   # React components
│   │   ├── auth/     # Authentication components
│   │   ├── common/   # Shared components
│   │   ├── dashboard/# Dashboard components
│   │   ├── features/ # Feature-specific components
│   │   ├── layout/   # Layout components
│   │   └── ui/       # UI components
│   ├── config/       # Frontend configuration
│   ├── contexts/     # React contexts
│   ├── hooks/        # Custom React hooks
│   ├── pages/        # Page components
│   ├── services/     # API services
│   └── utils/        # Utility functions
├── uploads/          # User uploads directory
├── .env              # Environment variables (not in git)
├── .gitignore        # Git ignore rules
├── config-overrides.js # Webpack configuration
├── ecosystem.config.js # PM2 configuration
├── package.json      # Project configuration
└── server.js         # Main server file
```

## Prerequisites

- Node.js (v14 or higher)
- PostgreSQL (v12 or higher)
- PM2 (for production deployment)

## Environment Variables

Create a `.env` file in the root directory with the following variables:

```env
# Server
PORT=3000
NODE_ENV=development

# Database
DB_HOST=localhost
DB_PORT=5432
DB_NAME=lux_voyage
DB_USER=postgres
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your_jwt_secret
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# Email
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false
EMAIL_USER=your_email
EMAIL_PASS=your_password

# Frontend
REACT_APP_API_URL=http://localhost:3000
REACT_APP_CDN_URL=http://localhost:3000/uploads

# Production
CORS_ORIGIN=https://your-domain.com
```

## Installation

1. Clone the repository:
```bash
git clone https://github.com/your-username/lux-voyage.git
cd lux-voyage
```

2. Install dependencies:
```bash
npm install
```

3. Set up the database:
```bash
npm run db:migrate
```

4. Start the development server:
```bash
npm run dev
```

## Available Scripts

### Development
- `npm start` - Start the React development server
- `npm run server` - Start the backend server
- `npm run dev` - Start both frontend and backend in development mode
- `npm run restart:clean` - Restart application with clean setup

### Database Management
- `npm run db:backup` - Create database backup
- `npm run db:migrate` - Run database migrations
- `npm run db:clear` - Clear dummy data from database

### Production
- `npm run build` - Build the frontend for production
- `npm run build:prod` - Build both frontend and backend for production
- `npm run deploy:prod` - Deploy to production
- `npm run deploy:rollback` - Rollback to previous version

### Code Quality
- `npm run lint` - Run ESLint
- `npm run format` - Format code with Prettier

## Deployment

1. Set up production environment variables
2. Build the application:
```bash
npm run build:prod
```

3. Deploy to production:
```bash
npm run deploy:prod
```

To rollback to a previous version:
```bash
npm run deploy:rollback
```

## Security Features

- JWT-based authentication
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation
- SQL injection prevention
- XSS protection

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.
