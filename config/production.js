const defaultConfig = require('./default');

module.exports = {
  ...defaultConfig,
  server: {
    ...defaultConfig.server,
    env: 'production',
    cors: {
      ...defaultConfig.server.cors,
      origin: process.env.CORS_ORIGIN // Must be set in production
    }
  },
  logging: {
    ...defaultConfig.logging,
    level: 'warn'
  },
  security: {
    ...defaultConfig.security,
    rateLimit: {
      windowMs: 15 * 60 * 1000,
      max: 50 // Stricter rate limiting in production
    }
  }
}; 