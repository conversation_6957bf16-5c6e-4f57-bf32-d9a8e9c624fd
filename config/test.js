const defaultConfig = require('./default');

module.exports = {
  ...defaultConfig,
  server: {
    ...defaultConfig.server,
    port: 3001, // Use different port for testing
    env: 'test'
  },
  database: {
    ...defaultConfig.database,
    name: process.env.TEST_DB_NAME || 'lux_voyage_test'
  },
  logging: {
    ...defaultConfig.logging,
    level: 'error',
    file: 'logs/test.log'
  },
  security: {
    ...defaultConfig.security,
    rateLimit: {
      windowMs: 15 * 60 * 1000,
      max: 1000 // Higher limit for testing
    }
  }
}; 