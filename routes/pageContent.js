const express = require('express');
const router = express.Router();
const { db } = require('../database/init');
const auth = require('../middleware/auth');

// Get page content by page name
router.get('/:pageName', (req, res) => {
  try {
    const { pageName } = req.params;
    
    const pageContent = db.prepare('SELECT * FROM page_content WHERE page_name = ?').get(pageName);
    
    if (!pageContent) {
      return res.status(404).json({ error: 'Page content not found' });
    }

    // Parse JSON content
    const content = JSON.parse(pageContent.content);
    
    res.json({
      id: pageContent.id,
      pageName: pageContent.page_name,
      content,
      metaTitle: pageContent.meta_title,
      metaDescription: pageContent.meta_description,
      createdAt: pageContent.created_at,
      updatedAt: pageContent.updated_at
    });
  } catch (error) {
    console.error('Error fetching page content:', error);
    res.status(500).json({ error: 'Failed to fetch page content' });
  }
});

// Get all page content (admin only)
router.get('/', auth, (req, res) => {
  try {
    const pageContents = db.prepare('SELECT * FROM page_content ORDER BY page_name').all();
    
    const formattedContents = pageContents.map(page => ({
      id: page.id,
      pageName: page.page_name,
      content: JSON.parse(page.content),
      metaTitle: page.meta_title,
      metaDescription: page.meta_description,
      createdAt: page.created_at,
      updatedAt: page.updated_at
    }));
    
    res.json(formattedContents);
  } catch (error) {
    console.error('Error fetching all page content:', error);
    res.status(500).json({ error: 'Failed to fetch page content' });
  }
});

// Update page content (admin only)
router.put('/:pageName', auth, (req, res) => {
  try {
    const { pageName } = req.params;
    const { content, metaTitle, metaDescription } = req.body;

    if (!content) {
      return res.status(400).json({ error: 'Content is required' });
    }

    // Convert content to JSON string if it's an object
    const contentString = typeof content === 'string' ? content : JSON.stringify(content);

    const updateStmt = db.prepare(`
      UPDATE page_content 
      SET content = ?, meta_title = ?, meta_description = ?, updated_at = CURRENT_TIMESTAMP
      WHERE page_name = ?
    `);

    const result = updateStmt.run(contentString, metaTitle, metaDescription, pageName);

    if (result.changes === 0) {
      // If no rows were updated, create new page content
      const insertStmt = db.prepare(`
        INSERT INTO page_content (page_name, content, meta_title, meta_description)
        VALUES (?, ?, ?, ?)
      `);

      insertStmt.run(pageName, contentString, metaTitle, metaDescription);
    }

    // Return updated content
    const updatedContent = db.prepare('SELECT * FROM page_content WHERE page_name = ?').get(pageName);
    
    res.json({
      id: updatedContent.id,
      pageName: updatedContent.page_name,
      content: JSON.parse(updatedContent.content),
      metaTitle: updatedContent.meta_title,
      metaDescription: updatedContent.meta_description,
      createdAt: updatedContent.created_at,
      updatedAt: updatedContent.updated_at
    });
  } catch (error) {
    console.error('Error updating page content:', error);
    res.status(500).json({ error: 'Failed to update page content' });
  }
});

// Create new page content (admin only)
router.post('/', auth, (req, res) => {
  try {
    const { pageName, content, metaTitle, metaDescription } = req.body;

    if (!pageName || !content) {
      return res.status(400).json({ error: 'Page name and content are required' });
    }

    // Convert content to JSON string if it's an object
    const contentString = typeof content === 'string' ? content : JSON.stringify(content);

    const insertStmt = db.prepare(`
      INSERT INTO page_content (page_name, content, meta_title, meta_description)
      VALUES (?, ?, ?, ?)
    `);

    const result = insertStmt.run(pageName, contentString, metaTitle, metaDescription);

    // Return created content
    const newContent = db.prepare('SELECT * FROM page_content WHERE id = ?').get(result.lastInsertRowid);
    
    res.status(201).json({
      id: newContent.id,
      pageName: newContent.page_name,
      content: JSON.parse(newContent.content),
      metaTitle: newContent.meta_title,
      metaDescription: newContent.meta_description,
      createdAt: newContent.created_at,
      updatedAt: newContent.updated_at
    });
  } catch (error) {
    console.error('Error creating page content:', error);
    if (error.code === 'SQLITE_CONSTRAINT_UNIQUE') {
      res.status(409).json({ error: 'Page content already exists' });
    } else {
      res.status(500).json({ error: 'Failed to create page content' });
    }
  }
});

// Delete page content (admin only)
router.delete('/:pageName', auth, (req, res) => {
  try {
    const { pageName } = req.params;

    const deleteStmt = db.prepare('DELETE FROM page_content WHERE page_name = ?');
    const result = deleteStmt.run(pageName);

    if (result.changes === 0) {
      return res.status(404).json({ error: 'Page content not found' });
    }

    res.json({ message: 'Page content deleted successfully' });
  } catch (error) {
    console.error('Error deleting page content:', error);
    res.status(500).json({ error: 'Failed to delete page content' });
  }
});

module.exports = router;
