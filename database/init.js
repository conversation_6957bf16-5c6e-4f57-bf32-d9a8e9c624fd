const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcryptjs');

// Create database directory if it doesn't exist
const dbDir = path.join(__dirname);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Initialize database
const db = new Database(path.join(dbDir, process.env.DB_PATH || 'luxvoyage.db'));

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Create tables
const createTables = () => {
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      role TEXT DEFAULT 'editor' CHECK (role IN ('admin', 'editor')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Destinations table
  db.exec(`
    CREATE TABLE IF NOT EXISTS destinations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      description TEXT,
      content TEXT,
      image_url TEXT,
      category TEXT NOT NULL,
      featured BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Experiences table
  db.exec(`
    CREATE TABLE IF NOT EXISTS experiences (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      description TEXT,
      content TEXT,
      image_url TEXT,
      category TEXT NOT NULL,
      duration TEXT,
      price DECIMAL(10,2),
      featured BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Offers table
  db.exec(`
    CREATE TABLE IF NOT EXISTS offers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      description TEXT,
      content TEXT,
      image_url TEXT,
      discount INTEGER NOT NULL,
      valid_from DATE NOT NULL,
      valid_to DATE NOT NULL,
      featured BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Inquiries table
  db.exec(`
    CREATE TABLE IF NOT EXISTS inquiries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT NOT NULL,
      phone TEXT,
      subject TEXT,
      message TEXT NOT NULL,
      status TEXT DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'replied')),
      reply TEXT,
      replied_at DATETIME,
      replied_by INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (replied_by) REFERENCES users(id)
    )
  `);

  // Media table
  db.exec(`
    CREATE TABLE IF NOT EXISTS media (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      alt TEXT,
      description TEXT,
      url TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('image', 'video', 'document')),
      size TEXT,
      created_by INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (created_by) REFERENCES users(id)
    )
  `);

  // Settings table
  db.exec(`
    CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      group_name TEXT NOT NULL,
      key_name TEXT NOT NULL,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(group_name, key_name)
    )
  `);

  console.log('Database tables created successfully');
};

// Insert default data
const insertDefaultData = async () => {
  // Check if users exist
  const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
  
  if (userCount.count === 0) {
    // Hash passwords before inserting
    const adminPassword = await bcrypt.hash('admin123', 10);
    const editorPassword = await bcrypt.hash('editor123', 10);

    // Insert default users
    const insertUser = db.prepare(`
      INSERT INTO users (username, password, name, email, role)
      VALUES (?, ?, ?, ?, ?)
    `);

    insertUser.run('admin', adminPassword, 'Admin User', '<EMAIL>', 'admin');
    insertUser.run('editor', editorPassword, 'Editor User', '<EMAIL>', 'editor');

    console.log('Default users inserted');
  }

  // Check if destinations exist
  const destCount = db.prepare('SELECT COUNT(*) as count FROM destinations').get();
  
  if (destCount.count === 0) {
    // Insert default destinations
    const insertDest = db.prepare(`
      INSERT INTO destinations (title, slug, description, content, image_url, category, featured)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    insertDest.run(
      'Luxury Resorts',
      'luxury-resorts',
      'Experience the ultimate luxury in our handpicked resorts.',
      '<p>Our luxury resorts offer the finest accommodations, world-class dining, and exceptional service.</p>',
      '/uploads/destination-luxury-resorts.jpg',
      'resorts',
      1
    );

    insertDest.run(
      'Private Islands',
      'private-islands',
      'Escape to your own private paradise on an exclusive island.',
      '<p>Experience the ultimate privacy and luxury on your own private island.</p>',
      '/uploads/destination-private-islands.jpg',
      'private-islands',
      1
    );

    insertDest.run(
      'Boutique Hotels',
      'boutique-hotels',
      'Discover unique and intimate boutique hotels with character.',
      '<p>Our carefully curated selection of boutique hotels offers intimate settings.</p>',
      '/uploads/destination-boutique-hotels.jpg',
      'hotels',
      0
    );

    console.log('Default destinations inserted');
  }

  // Insert default settings
  const settingsCount = db.prepare('SELECT COUNT(*) as count FROM settings').get();
  
  if (settingsCount.count === 0) {
    const insertSetting = db.prepare(`
      INSERT INTO settings (group_name, key_name, value)
      VALUES (?, ?, ?)
    `);

    const defaultSettings = [
      ['general', 'siteTitle', 'Lux Voyage Travel Agency'],
      ['general', 'siteDescription', 'Luxury travel experiences in the Maldives'],
      ['general', 'logoUrl', '/images/logo.png'],
      ['general', 'faviconUrl', '/favicon.ico'],
      ['general', 'defaultLanguage', 'en'],
      ['general', 'maintenanceMode', 'false'],
      ['appearance', 'primaryColor', '#0099b8'],
      ['appearance', 'secondaryColor', '#ff9800'],
      ['appearance', 'fontFamily', 'Inter'],
      ['appearance', 'headingFontFamily', 'Cormorant Garamond'],
      ['appearance', 'buttonStyle', 'rounded']
    ];

    defaultSettings.forEach(([group, key, value]) => {
      insertSetting.run(group, key, value);
    });

    console.log('Default settings inserted');
  }
};

// Initialize database
const initializeDatabase = async () => {
  try {
    createTables();
    await insertDefaultData();
    console.log('Database initialized successfully');
    return db;
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
};

module.exports = { initializeDatabase, db };
