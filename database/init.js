const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const bcrypt = require('bcryptjs');

// Create database directory if it doesn't exist
const dbDir = path.join(__dirname);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Initialize database
const db = new Database(path.join(dbDir, process.env.DB_PATH || 'luxvoyage.db'));

// Enable foreign keys
db.pragma('foreign_keys = ON');

// Create tables
const createTables = () => {
  // Users table
  db.exec(`
    CREATE TABLE IF NOT EXISTS users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password TEXT NOT NULL,
      name TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      role TEXT DEFAULT 'editor' CHECK (role IN ('admin', 'editor')),
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Destinations table
  db.exec(`
    CREATE TABLE IF NOT EXISTS destinations (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      description TEXT,
      content TEXT,
      image_url TEXT,
      category TEXT NOT NULL,
      featured BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Experiences table
  db.exec(`
    CREATE TABLE IF NOT EXISTS experiences (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      description TEXT,
      content TEXT,
      image_url TEXT,
      category TEXT NOT NULL,
      duration TEXT,
      price DECIMAL(10,2),
      featured BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Offers table
  db.exec(`
    CREATE TABLE IF NOT EXISTS offers (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      description TEXT,
      content TEXT,
      image_url TEXT,
      discount INTEGER NOT NULL,
      valid_from DATE NOT NULL,
      valid_to DATE NOT NULL,
      featured BOOLEAN DEFAULT 0,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Inquiries table
  db.exec(`
    CREATE TABLE IF NOT EXISTS inquiries (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT NOT NULL,
      phone TEXT,
      subject TEXT,
      message TEXT NOT NULL,
      status TEXT DEFAULT 'unread' CHECK (status IN ('unread', 'read', 'replied')),
      reply TEXT,
      replied_at DATETIME,
      replied_by INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (replied_by) REFERENCES users(id)
    )
  `);

  // Media table
  db.exec(`
    CREATE TABLE IF NOT EXISTS media (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      alt TEXT,
      description TEXT,
      url TEXT NOT NULL,
      type TEXT NOT NULL CHECK (type IN ('image', 'video', 'document')),
      size TEXT,
      created_by INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (created_by) REFERENCES users(id)
    )
  `);

  // Settings table
  db.exec(`
    CREATE TABLE IF NOT EXISTS settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      group_name TEXT NOT NULL,
      key_name TEXT NOT NULL,
      value TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      UNIQUE(group_name, key_name)
    )
  `);

  // Page content table for managing editable page content
  db.exec(`
    CREATE TABLE IF NOT EXISTS page_content (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      page_name TEXT NOT NULL UNIQUE,
      content TEXT NOT NULL,
      meta_title TEXT,
      meta_description TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  console.log('Database tables created successfully');
};

// Insert default data
const insertDefaultData = async () => {
  // Check if users exist
  const userCount = db.prepare('SELECT COUNT(*) as count FROM users').get();
  
  if (userCount.count === 0) {
    // Hash passwords before inserting
    const adminPassword = await bcrypt.hash('admin123', 10);
    const editorPassword = await bcrypt.hash('editor123', 10);

    // Insert default users
    const insertUser = db.prepare(`
      INSERT INTO users (username, password, name, email, role)
      VALUES (?, ?, ?, ?, ?)
    `);

    insertUser.run('admin', adminPassword, 'Admin User', '<EMAIL>', 'admin');
    insertUser.run('editor', editorPassword, 'Editor User', '<EMAIL>', 'editor');

    console.log('Default users inserted');
  }

  // Note: No default destinations inserted - ready for real data
  console.log('Database ready for real destination data');

  // Insert default settings
  const settingsCount = db.prepare('SELECT COUNT(*) as count FROM settings').get();
  
  if (settingsCount.count === 0) {
    const insertSetting = db.prepare(`
      INSERT INTO settings (group_name, key_name, value)
      VALUES (?, ?, ?)
    `);

    const defaultSettings = [
      ['general', 'siteTitle', 'Lux Voyage Travel Agency'],
      ['general', 'siteDescription', 'Premium travel experiences and luxury destinations'],
      ['general', 'logoUrl', '/images/logo.png'],
      ['general', 'faviconUrl', '/favicon.ico'],
      ['general', 'defaultLanguage', 'en'],
      ['general', 'maintenanceMode', 'false'],
      ['general', 'contactEmail', '<EMAIL>'],
      ['general', 'contactPhone', '******-LUX-VOYAGE'],
      ['appearance', 'primaryColor', '#0099b8'],
      ['appearance', 'secondaryColor', '#ff9800'],
      ['appearance', 'fontFamily', 'Inter'],
      ['appearance', 'headingFontFamily', 'Cormorant Garamond'],
      ['appearance', 'buttonStyle', 'rounded'],
      ['content', 'heroTitle', 'Welcome to Lux Voyage'],
      ['content', 'heroSubtitle', 'Discover extraordinary travel experiences'],
      ['content', 'aboutText', 'Your premier destination for luxury travel experiences.']
    ];

    defaultSettings.forEach(([group, key, value]) => {
      insertSetting.run(group, key, value);
    });

    console.log('Default settings inserted');
  }

  // Insert default page content
  const pageContentCount = db.prepare('SELECT COUNT(*) as count FROM page_content').get();

  if (pageContentCount.count === 0) {
    const insertPageContent = db.prepare(`
      INSERT INTO page_content (page_name, content, meta_title, meta_description)
      VALUES (?, ?, ?, ?)
    `);

    const defaultAboutContent = JSON.stringify({
      hero: {
        title: "About Lux Voyage",
        subtitle: "Discover our story, vision, and commitment to creating extraordinary travel experiences in the Maldives"
      },
      companyProfile: {
        title: "Our Story",
        subtitle: "Founded with a passion for luxury travel and the pristine beauty of the Maldives",
        mainTitle: "Welcome to Lux Voyage Travel Agency",
        description: "Established as a premier travel agency specializing in luxury Maldivian experiences, Lux Voyage has been crafting unforgettable journeys to paradise for discerning travelers worldwide. Our deep understanding of the Maldives' unique charm and our commitment to excellence have made us a trusted partner for those seeking the ultimate tropical escape.",
        secondDescription: "From the moment you begin planning your journey with us, our dedicated team works tirelessly to ensure every detail of your Maldivian adventure exceeds your expectations. We believe that travel is not just about reaching a destination—it's about creating memories that last a lifetime.",
        features: [
          "Personalized luxury travel experiences",
          "Expert knowledge of Maldivian destinations",
          "24/7 customer support and assistance",
          "Exclusive partnerships with premium resorts",
          "Sustainable and responsible tourism practices"
        ],
        stats: [
          { number: "500+", label: "Happy Travelers" },
          { number: "50+", label: "Partner Resorts" },
          { number: "15+", label: "Years Experience" },
          { number: "98%", label: "Customer Satisfaction" }
        ]
      },
      vision: {
        title: "Our Vision",
        content: "To be the world's leading luxury travel agency for Maldivian experiences, recognized for our unparalleled service, deep local expertise, and commitment to creating transformative journeys that connect travelers with the natural beauty and cultural richness of the Maldives."
      },
      mission: {
        title: "Our Mission",
        content: "To curate and deliver exceptional, personalized luxury travel experiences in the Maldives that exceed our clients' expectations while promoting sustainable tourism practices that preserve the natural beauty and cultural heritage of these pristine islands for future generations."
      },
      values: [
        {
          icon: "🏆",
          title: "Excellence",
          description: "We strive for perfection in every aspect of our service, from initial consultation to your safe return home."
        },
        {
          icon: "🤝",
          title: "Trust",
          description: "Building lasting relationships through transparency, reliability, and honest communication with our clients."
        },
        {
          icon: "🌱",
          title: "Sustainability",
          description: "Committed to responsible tourism that protects the Maldives' pristine environment for future generations."
        },
        {
          icon: "💎",
          title: "Luxury",
          description: "Curating premium experiences that offer the finest accommodations, services, and exclusive access."
        },
        {
          icon: "🎨",
          title: "Personalization",
          description: "Every journey is uniquely crafted to match your individual preferences, interests, and travel style."
        },
        {
          icon: "🌍",
          title: "Cultural Respect",
          description: "Honoring and celebrating the rich Maldivian culture while creating authentic, respectful experiences."
        }
      ],
      whyChooseUs: {
        title: "Why Choose Lux Voyage?",
        description: "With countless travel agencies offering Maldivian experiences, what sets Lux Voyage apart is our unwavering commitment to excellence and our deep-rooted passion for these magnificent islands.",
        secondDescription: "Our team consists of travel specialists who have personally explored every resort, experienced every activity, and built relationships with local partners across the Maldives. This firsthand knowledge allows us to provide insider insights and recommendations that you simply won't find elsewhere.",
        features: [
          "Exclusive access to private resorts and experiences",
          "Competitive pricing through direct partnerships",
          "Comprehensive travel insurance and protection",
          "Multi-language customer support",
          "Emergency assistance and 24/7 concierge services",
          "Flexible booking and cancellation policies"
        ]
      }
    });

    insertPageContent.run(
      'about-us',
      defaultAboutContent,
      'About Us - Lux Voyage Travel Agency',
      'Learn about Lux Voyage Travel Agency, our story, vision, mission, and commitment to creating extraordinary luxury travel experiences in the Maldives.'
    );

    console.log('Default page content inserted');
  }
};

// Initialize database
const initializeDatabase = async () => {
  try {
    createTables();
    await insertDefaultData();
    console.log('Database initialized successfully');
    return db;
  } catch (error) {
    console.error('Database initialization failed:', error);
    throw error;
  }
};

module.exports = { initializeDatabase, db };
