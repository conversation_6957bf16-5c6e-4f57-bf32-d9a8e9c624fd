const { db } = require('./init');

// Base Model class with common CRUD operations
class BaseModel {
  constructor(tableName) {
    this.tableName = tableName;
    this.db = db;
  }

  // Get all records
  getAll() {
    return this.db.prepare(`SELECT * FROM ${this.tableName} ORDER BY created_at DESC`).all();
  }

  // Get record by ID
  getById(id) {
    return this.db.prepare(`SELECT * FROM ${this.tableName} WHERE id = ?`).get(id);
  }

  // Delete record by ID
  deleteById(id) {
    const result = this.db.prepare(`DELETE FROM ${this.tableName} WHERE id = ?`).run(id);
    return result.changes > 0;
  }

  // Get count of records
  getCount() {
    return this.db.prepare(`SELECT COUNT(*) as count FROM ${this.tableName}`).get().count;
  }
}

// User Model
class UserModel extends BaseModel {
  constructor() {
    super('users');
  }

  getByUsername(username) {
    return this.db.prepare('SELECT * FROM users WHERE username = ?').get(username);
  }

  getByEmail(email) {
    return this.db.prepare('SELECT * FROM users WHERE email = ?').get(email);
  }

  create(userData) {
    const { username, password, name, email, role = 'editor' } = userData;
    const stmt = this.db.prepare(`
      INSERT INTO users (username, password, name, email, role)
      VALUES (?, ?, ?, ?, ?)
    `);
    const result = stmt.run(username, password, name, email, role);
    return this.getById(result.lastInsertRowid);
  }

  update(id, userData) {
    const { username, name, email, role } = userData;
    const stmt = this.db.prepare(`
      UPDATE users 
      SET username = ?, name = ?, email = ?, role = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    const result = stmt.run(username, name, email, role, id);
    return result.changes > 0 ? this.getById(id) : null;
  }
}

// Destination Model
class DestinationModel extends BaseModel {
  constructor() {
    super('destinations');
  }

  getBySlug(slug) {
    return this.db.prepare('SELECT * FROM destinations WHERE slug = ?').get(slug);
  }

  getFeatured() {
    return this.db.prepare('SELECT * FROM destinations WHERE featured = 1 ORDER BY created_at DESC').all();
  }

  getByCategory(category) {
    return this.db.prepare('SELECT * FROM destinations WHERE category = ? ORDER BY created_at DESC').all(category);
  }

  create(destData) {
    const { title, slug, description, content, image_url, category, featured = 0 } = destData;
    const stmt = this.db.prepare(`
      INSERT INTO destinations (title, slug, description, content, image_url, category, featured)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    const result = stmt.run(title, slug, description, content, image_url, category, featured);
    return this.getById(result.lastInsertRowid);
  }

  update(id, destData) {
    const { title, slug, description, content, image_url, category, featured } = destData;
    const stmt = this.db.prepare(`
      UPDATE destinations 
      SET title = ?, slug = ?, description = ?, content = ?, image_url = ?, category = ?, featured = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    const result = stmt.run(title, slug, description, content, image_url, category, featured, id);
    return result.changes > 0 ? this.getById(id) : null;
  }
}

// Experience Model
class ExperienceModel extends BaseModel {
  constructor() {
    super('experiences');
  }

  getBySlug(slug) {
    return this.db.prepare('SELECT * FROM experiences WHERE slug = ?').get(slug);
  }

  getFeatured() {
    return this.db.prepare('SELECT * FROM experiences WHERE featured = 1 ORDER BY created_at DESC').all();
  }

  getByCategory(category) {
    return this.db.prepare('SELECT * FROM experiences WHERE category = ? ORDER BY created_at DESC').all(category);
  }

  create(expData) {
    const { title, slug, description, content, image_url, category, duration, price, featured = 0 } = expData;
    const stmt = this.db.prepare(`
      INSERT INTO experiences (title, slug, description, content, image_url, category, duration, price, featured)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    const result = stmt.run(title, slug, description, content, image_url, category, duration, price, featured);
    return this.getById(result.lastInsertRowid);
  }

  update(id, expData) {
    const { title, slug, description, content, image_url, category, duration, price, featured } = expData;
    const stmt = this.db.prepare(`
      UPDATE experiences 
      SET title = ?, slug = ?, description = ?, content = ?, image_url = ?, category = ?, duration = ?, price = ?, featured = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    const result = stmt.run(title, slug, description, content, image_url, category, duration, price, featured, id);
    return result.changes > 0 ? this.getById(id) : null;
  }
}

// Offer Model
class OfferModel extends BaseModel {
  constructor() {
    super('offers');
  }

  getBySlug(slug) {
    return this.db.prepare('SELECT * FROM offers WHERE slug = ?').get(slug);
  }

  getFeatured() {
    return this.db.prepare('SELECT * FROM offers WHERE featured = 1 ORDER BY created_at DESC').all();
  }

  getActive() {
    const today = new Date().toISOString().split('T')[0];
    return this.db.prepare('SELECT * FROM offers WHERE valid_from <= ? AND valid_to >= ? ORDER BY created_at DESC').all(today, today);
  }

  create(offerData) {
    const { title, slug, description, content, image_url, discount, valid_from, valid_to, featured = 0 } = offerData;
    const stmt = this.db.prepare(`
      INSERT INTO offers (title, slug, description, content, image_url, discount, valid_from, valid_to, featured)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);
    const result = stmt.run(title, slug, description, content, image_url, discount, valid_from, valid_to, featured);
    return this.getById(result.lastInsertRowid);
  }

  update(id, offerData) {
    const { title, slug, description, content, image_url, discount, valid_from, valid_to, featured } = offerData;
    const stmt = this.db.prepare(`
      UPDATE offers 
      SET title = ?, slug = ?, description = ?, content = ?, image_url = ?, discount = ?, valid_from = ?, valid_to = ?, featured = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    const result = stmt.run(title, slug, description, content, image_url, discount, valid_from, valid_to, featured, id);
    return result.changes > 0 ? this.getById(id) : null;
  }
}

// Inquiry Model
class InquiryModel extends BaseModel {
  constructor() {
    super('inquiries');
  }

  getUnread() {
    return this.db.prepare('SELECT * FROM inquiries WHERE status = "unread" ORDER BY created_at DESC').all();
  }

  create(inquiryData) {
    const { name, email, phone, subject, message } = inquiryData;
    const stmt = this.db.prepare(`
      INSERT INTO inquiries (name, email, phone, subject, message)
      VALUES (?, ?, ?, ?, ?)
    `);
    const result = stmt.run(name, email, phone || '', subject || 'General Inquiry', message);
    return this.getById(result.lastInsertRowid);
  }

  markAsRead(id) {
    const stmt = this.db.prepare('UPDATE inquiries SET status = "read", updated_at = CURRENT_TIMESTAMP WHERE id = ?');
    const result = stmt.run(id);
    return result.changes > 0 ? this.getById(id) : null;
  }

  reply(id, reply, userId) {
    const stmt = this.db.prepare(`
      UPDATE inquiries 
      SET status = "replied", reply = ?, replied_at = CURRENT_TIMESTAMP, replied_by = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    const result = stmt.run(reply, userId, id);
    return result.changes > 0 ? this.getById(id) : null;
  }
}

// Media Model
class MediaModel extends BaseModel {
  constructor() {
    super('media');
  }

  create(mediaData) {
    const { title, alt, description, url, type, size, created_by } = mediaData;
    const stmt = this.db.prepare(`
      INSERT INTO media (title, alt, description, url, type, size, created_by)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    const result = stmt.run(title, alt || '', description || '', url, type, size, created_by);
    return this.getById(result.lastInsertRowid);
  }

  update(id, mediaData) {
    const { title, alt, description } = mediaData;
    const stmt = this.db.prepare(`
      UPDATE media 
      SET title = ?, alt = ?, description = ?, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `);
    const result = stmt.run(title, alt, description, id);
    return result.changes > 0 ? this.getById(id) : null;
  }
}

// Settings Model
class SettingsModel extends BaseModel {
  constructor() {
    super('settings');
  }

  getByKey(key) {
    return this.db.prepare('SELECT * FROM settings WHERE key_name = ?').get(key);
  }

  getByGroup(group) {
    return this.db.prepare('SELECT * FROM settings WHERE group_name = ? ORDER BY key_name').all(group);
  }

  updateByKey(key, value) {
    const stmt = this.db.prepare('UPDATE settings SET value = ?, updated_at = CURRENT_TIMESTAMP WHERE key_name = ?');
    const result = stmt.run(value, key);
    return result.changes > 0;
  }

  create(group, key, value) {
    const stmt = this.db.prepare(`
      INSERT INTO settings (group_name, key_name, value)
      VALUES (?, ?, ?)
    `);
    const result = stmt.run(group, key, value);
    return this.getById(result.lastInsertRowid);
  }
}

// Export model instances
module.exports = {
  User: new UserModel(),
  Destination: new DestinationModel(),
  Experience: new ExperienceModel(),
  Offer: new OfferModel(),
  Inquiry: new InquiryModel(),
  Media: new MediaModel(),
  Settings: new SettingsModel()
};
