#!/usr/bin/env node

/**
 * <PERSON>ript to clear dummy data from the database
 * This prepares the database for real production data
 */

const Database = require('better-sqlite3');
const path = require('path');
require('dotenv').config();

const dbPath = path.join(__dirname, '..', 'database', process.env.DB_PATH || 'luxvoyage.db');

console.log('🧹 Clearing dummy data from database...');
console.log(`Database path: ${dbPath}`);

try {
  const db = new Database(dbPath);
  
  // Enable foreign keys
  db.pragma('foreign_keys = ON');
  
  console.log('\n📋 Current data counts:');
  
  // Show current counts
  const tables = ['destinations', 'experiences', 'offers', 'inquiries', 'media'];
  const counts = {};
  
  tables.forEach(table => {
    const count = db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get().count;
    counts[table] = count;
    console.log(`  ${table}: ${count} records`);
  });
  
  // Clear dummy data (keep users and settings)
  console.log('\n🗑️  Clearing dummy data...');
  
  // Clear destinations
  if (counts.destinations > 0) {
    const result = db.prepare('DELETE FROM destinations').run();
    console.log(`  ✅ Cleared ${result.changes} destinations`);
  }
  
  // Clear experiences
  if (counts.experiences > 0) {
    const result = db.prepare('DELETE FROM experiences').run();
    console.log(`  ✅ Cleared ${result.changes} experiences`);
  }
  
  // Clear offers
  if (counts.offers > 0) {
    const result = db.prepare('DELETE FROM offers').run();
    console.log(`  ✅ Cleared ${result.changes} offers`);
  }
  
  // Clear inquiries (optional - you might want to keep real inquiries)
  if (counts.inquiries > 0) {
    console.log(`  ⚠️  Found ${counts.inquiries} inquiries - keeping them (might be real data)`);
  }
  
  // Clear media (be careful - might contain real uploaded files)
  if (counts.media > 0) {
    console.log(`  ⚠️  Found ${counts.media} media files - keeping them (might be real uploads)`);
  }
  
  // Reset auto-increment counters
  console.log('\n🔄 Resetting auto-increment counters...');
  db.prepare('DELETE FROM sqlite_sequence WHERE name IN (?, ?, ?)').run('destinations', 'experiences', 'offers');
  console.log('  ✅ Auto-increment counters reset');
  
  // Vacuum database to reclaim space
  console.log('\n🧽 Optimizing database...');
  db.exec('VACUUM');
  console.log('  ✅ Database optimized');
  
  db.close();
  
  console.log('\n✨ Database cleared and ready for real data!');
  console.log('\n📝 Next steps:');
  console.log('  1. Start the application: npm run dev');
  console.log('  2. Login to admin dashboard with: admin / admin123');
  console.log('  3. Add your real destinations, experiences, and offers');
  console.log('  4. Upload real media files');
  console.log('  5. Update site settings as needed');
  
} catch (error) {
  console.error('❌ Error clearing dummy data:', error.message);
  process.exit(1);
}
