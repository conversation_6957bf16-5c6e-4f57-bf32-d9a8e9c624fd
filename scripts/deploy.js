const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const config = {
  server: {
    host: process.env.DEPLOY_HOST,
    user: process.env.DEPLOY_USER,
    port: process.env.DEPLOY_PORT || '22',
    path: process.env.DEPLOY_PATH
  },
  build: {
    dir: path.join(__dirname, '../build'),
    temp: path.join(__dirname, '../dist')
  }
};

// Validate environment variables
const requiredEnvVars = [
  'DEPLOY_HOST',
  'DEPLOY_USER',
  'DEPLOY_PATH',
  'REACT_APP_API_URL',
  'REACT_APP_CDN_URL'
];

const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars.join(', '));
  process.exit(1);
}

// Create temporary build directory
if (!fs.existsSync(config.build.temp)) {
  fs.mkdirSync(config.build.temp, { recursive: true });
}

try {
  console.log('Starting deployment process...');

  // Build the application
  console.log('Building application...');
  execSync('npm run build:prod', { stdio: 'inherit' });

  // Create deployment package
  console.log('Creating deployment package...');
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const packageName = `deploy-${timestamp}.tar.gz`;
  
  // Create tar archive
  execSync(`tar -czf ${packageName} -C ${config.build.dir} .`, { stdio: 'inherit' });

  // Upload to server
  console.log('Uploading to server...');
  execSync(
    `scp -P ${config.server.port} ${packageName} ${config.server.user}@${config.server.host}:${config.server.path}`,
    { stdio: 'inherit' }
  );

  // Execute deployment commands on server
  console.log('Executing deployment commands...');
  const deployCommands = [
    `cd ${config.server.path}`,
    'mkdir -p backup',
    `tar -czf backup/backup-${timestamp}.tar.gz .`,
    `tar -xzf ${packageName}`,
    'rm -f *.tar.gz',
    'npm install --production',
    'pm2 restart ecosystem.config.js || pm2 start ecosystem.config.js'
  ].join(' && ');

  execSync(
    `ssh -p ${config.server.port} ${config.server.user}@${config.server.host} "${deployCommands}"`,
    { stdio: 'inherit' }
  );

  // Clean up local files
  console.log('Cleaning up...');
  fs.unlinkSync(packageName);
  fs.rmSync(config.build.temp, { recursive: true, force: true });

  console.log('Deployment completed successfully!');
} catch (error) {
  console.error('Deployment failed:', error);
  process.exit(1);
} 