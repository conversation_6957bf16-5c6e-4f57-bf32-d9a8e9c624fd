const { db } = require('../database/init');

console.log('Testing page content API...');

try {
  const pageContent = db.prepare('SELECT * FROM page_content WHERE page_name = ?').get('about-us');
  
  if (!pageContent) {
    console.log('❌ Page content not found');
  } else {
    console.log('✅ Page content found:', {
      id: pageContent.id,
      pageName: pageContent.page_name,
      metaTitle: pageContent.meta_title,
      contentLength: pageContent.content ? pageContent.content.length : 0
    });
    
    // Try to parse the content
    try {
      const content = JSON.parse(pageContent.content);
      console.log('✅ Content parsed successfully');
      console.log('Content sections:', Object.keys(content));
    } catch (parseError) {
      console.log('❌ Content parse error:', parseError.message);
    }
  }
} catch (error) {
  console.error('❌ Database error:', error);
}
