const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Build directory
const buildDir = path.join(__dirname, '../build');

// Ensure build directory exists
if (!fs.existsSync(buildDir)) {
  fs.mkdirSync(buildDir, { recursive: true });
}

// Run build process
console.log('Starting production build...');

try {
  // Clean build directory
  console.log('Cleaning build directory...');
  if (fs.existsSync(buildDir)) {
    fs.rmSync(buildDir, { recursive: true, force: true });
  }

  // Install dependencies
  console.log('Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });

  // Build React app
  console.log('Building React app...');
  execSync('npm run build', { stdio: 'inherit' });

  // Create robots.txt
  console.log('Creating robots.txt...');
  const robotsContent = process.env.NODE_ENV === 'production'
    ? `User-agent: *
Allow: /
Sitemap: ${process.env.REACT_APP_API_URL}/sitemap.xml`
    : `User-agent: *
Disallow: /`;
  fs.writeFileSync(path.join(buildDir, 'robots.txt'), robotsContent);

  // Create sitemap.xml
  console.log('Creating sitemap.xml...');
  const sitemapContent = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <url>
    <loc>${process.env.REACT_APP_API_URL}</loc>
    <lastmod>${new Date().toISOString()}</lastmod>
    <changefreq>daily</changefreq>
    <priority>1.0</priority>
  </url>
</urlset>`;
  fs.writeFileSync(path.join(buildDir, 'sitemap.xml'), sitemapContent);

  // Create .htaccess for Apache
  console.log('Creating .htaccess...');
  const htaccessContent = `# Enable rewrite engine
RewriteEngine On

# Redirect to HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Handle React routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^ index.html [QSA,L]

# Enable CORS
Header set Access-Control-Allow-Origin "*"

# Cache control
<FilesMatch "\\.(css|js|jpg|jpeg|png|gif|ico|svg|woff|woff2|ttf|eot)$">
  Header set Cache-Control "max-age=********, public"
</FilesMatch>

# Security headers
Header set X-Content-Type-Options "nosniff"
Header set X-Frame-Options "SAMEORIGIN"
Header set X-XSS-Protection "1; mode=block"
Header set Referrer-Policy "strict-origin-when-cross-origin"`;
  fs.writeFileSync(path.join(buildDir, '.htaccess'), htaccessContent);

  console.log('Production build completed successfully!');
} catch (error) {
  console.error('Build failed:', error);
  process.exit(1);
} 