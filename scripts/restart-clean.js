#!/usr/bin/env node

/**
 * <PERSON><PERSON><PERSON> to restart the application with clean data
 * This script will:
 * 1. Stop any running processes
 * 2. Clear dummy data
 * 3. Restart the application
 */

const { spawn, exec } = require('child_process');
const path = require('path');

console.log('🔄 Restarting Lux Voyage with clean data...\n');

// Function to execute command and return promise
function execCommand(command, options = {}) {
  return new Promise((resolve, reject) => {
    exec(command, options, (error, stdout, stderr) => {
      if (error) {
        reject(error);
      } else {
        resolve({ stdout, stderr });
      }
    });
  });
}

// Function to kill processes on specific ports
async function killProcessOnPort(port) {
  try {
    const { stdout } = await execCommand(`lsof -ti:${port}`);
    if (stdout.trim()) {
      const pids = stdout.trim().split('\n');
      for (const pid of pids) {
        try {
          await execCommand(`kill -9 ${pid}`);
          console.log(`  ✅ Killed process ${pid} on port ${port}`);
        } catch (error) {
          console.log(`  ⚠️  Could not kill process ${pid}: ${error.message}`);
        }
      }
    } else {
      console.log(`  ℹ️  No processes running on port ${port}`);
    }
  } catch (error) {
    console.log(`  ℹ️  No processes running on port ${port}`);
  }
}

async function main() {
  try {
    // Step 1: Stop any running processes
    console.log('1️⃣  Stopping existing processes...');
    await killProcessOnPort(3000); // React dev server
    await killProcessOnPort(5000); // Express server
    
    // Also try to stop PM2 processes
    try {
      await execCommand('pm2 stop all');
      console.log('  ✅ Stopped PM2 processes');
    } catch (error) {
      console.log('  ℹ️  No PM2 processes to stop');
    }
    
    console.log('');

    // Step 2: Clear dummy data
    console.log('2️⃣  Clearing dummy data...');
    try {
      const { stdout } = await execCommand('npm run db:clear');
      console.log(stdout);
    } catch (error) {
      console.error('  ❌ Error clearing data:', error.message);
    }

    // Step 3: Wait a moment for cleanup
    console.log('3️⃣  Waiting for cleanup...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 4: Start the application
    console.log('4️⃣  Starting the application...\n');
    
    console.log('🚀 Starting backend server...');
    const backend = spawn('npm', ['run', 'server'], {
      stdio: 'inherit',
      detached: false
    });

    // Wait a moment for backend to start
    await new Promise(resolve => setTimeout(resolve, 3000));

    console.log('🎨 Starting frontend development server...');
    const frontend = spawn('npm', ['start'], {
      stdio: 'inherit',
      detached: false
    });

    // Handle process termination
    process.on('SIGINT', () => {
      console.log('\n🛑 Shutting down...');
      backend.kill();
      frontend.kill();
      process.exit(0);
    });

    console.log('\n✨ Application started successfully!');
    console.log('\n📋 Quick Start Guide:');
    console.log('  🌐 Frontend: http://localhost:3000');
    console.log('  🔧 Backend API: http://localhost:5000');
    console.log('  👤 Admin Login: admin / admin123');
    console.log('  📝 Admin Dashboard: http://localhost:3000/admin');
    console.log('\n💡 Next Steps:');
    console.log('  1. Login to the admin dashboard');
    console.log('  2. Add your real destinations, experiences, and offers');
    console.log('  3. Upload media files');
    console.log('  4. Customize site settings');
    console.log('\n⚠️  Note: The database is now clean and ready for real data!');
    console.log('  Press Ctrl+C to stop both servers');

  } catch (error) {
    console.error('❌ Error during restart:', error.message);
    process.exit(1);
  }
}

main();
