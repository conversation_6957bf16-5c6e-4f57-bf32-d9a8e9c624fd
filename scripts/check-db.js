const { db } = require('../database/init');

console.log('Checking database...');

try {
  // Check if page_content table exists
  const tables = db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='page_content'").all();
  console.log('page_content table exists:', tables.length > 0);

  if (tables.length > 0) {
    // Check table structure
    const columns = db.prepare("PRAGMA table_info(page_content)").all();
    console.log('Table columns:', columns.map(col => col.name));

    // Check data
    const count = db.prepare('SELECT COUNT(*) as count FROM page_content').get();
    console.log('Records in page_content:', count.count);

    if (count.count > 0) {
      const records = db.prepare('SELECT page_name, meta_title FROM page_content').all();
      console.log('Records:', records);
    }
  }
} catch (error) {
  console.error('Database check error:', error);
}
