const config = require('../config');
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('Starting environment test...\n');

// Test Node.js version
console.log('Checking Node.js version...');
const nodeVersion = process.version;
console.log(`Node.js version: ${nodeVersion}`);
if (parseInt(nodeVersion.slice(1).split('.')[0]) < 14) {
  console.error('Error: Node.js version 14 or higher is required');
  process.exit(1);
}
console.log('✓ Node.js version check passed\n');

// Test required directories
console.log('Checking required directories...');
const requiredDirs = [
  'src',
  'public',
  'config',
  'database',
  'uploads',
  'scripts'
];

requiredDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    console.error(`Error: Required directory '${dir}' is missing`);
    process.exit(1);
  }
  console.log(`✓ Directory '${dir}' exists`);
});
console.log('✓ All required directories exist\n');

// Test configuration
console.log('Testing configuration...');
try {
  const requiredConfig = [
    'server',
    'database',
    'jwt',
    'upload',
    'email',
    'logging',
    'security',
    'frontend'
  ];

  requiredConfig.forEach(key => {
    if (!config[key]) {
      throw new Error(`Missing configuration: ${key}`);
    }
  });
  console.log('✓ Configuration validation passed\n');
} catch (error) {
  console.error('Configuration error:', error.message);
  process.exit(1);
}

// Test database connection
console.log('Testing database connection...');
try {
  const { Pool } = require('pg');
  const pool = new Pool(config.database);
  
  pool.query('SELECT NOW()', (err, res) => {
    if (err) {
      console.error('Database connection error:', err.message);
      process.exit(1);
    }
    console.log('✓ Database connection successful\n');
    pool.end();
  });
} catch (error) {
  console.error('Database error:', error.message);
  process.exit(1);
}

// Test file permissions
console.log('Testing file permissions...');
const testDirs = ['uploads', 'logs'];
testDirs.forEach(dir => {
  try {
    const testFile = path.join(dir, '.test');
    fs.writeFileSync(testFile, 'test');
    fs.unlinkSync(testFile);
    console.log(`✓ Write permissions for '${dir}' directory`);
  } catch (error) {
    console.error(`Error: No write permissions for '${dir}' directory`);
    process.exit(1);
  }
});
console.log('✓ All file permissions are correct\n');

// Test npm scripts
console.log('Testing npm scripts...');
const requiredScripts = [
  'start',
  'build',
  'server',
  'dev',
  'build:prod',
  'deploy:prod',
  'deploy:rollback'
];

const packageJson = require('../package.json');
requiredScripts.forEach(script => {
  if (!packageJson.scripts[script]) {
    console.error(`Error: Required npm script '${script}' is missing`);
    process.exit(1);
  }
  console.log(`✓ Script '${script}' exists`);
});
console.log('✓ All required npm scripts exist\n');

console.log('Environment test completed successfully! ✨'); 