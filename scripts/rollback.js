const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Configuration
const config = {
  server: {
    host: process.env.DEPLOY_HOST,
    user: process.env.DEPLOY_USER,
    port: process.env.DEPLOY_PORT || '22',
    path: process.env.DEPLOY_PATH
  }
};

// Validate environment variables
const requiredEnvVars = ['DEPLOY_HOST', 'DEPLOY_USER', 'DEPLOY_PATH'];
const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName]);
if (missingEnvVars.length > 0) {
  console.error('Missing required environment variables:', missingEnvVars.join(', '));
  process.exit(1);
}

try {
  console.log('Starting rollback process...');

  // Get available backups
  console.log('Fetching available backups...');
  const backupList = execSync(
    `ssh -p ${config.server.port} ${config.server.user}@${config.server.host} "ls -t ${config.server.path}/backup"`,
    { stdio: 'pipe' }
  ).toString().trim().split('\n');

  if (backupList.length === 0) {
    console.error('No backups available for rollback');
    process.exit(1);
  }

  // Display available backups
  console.log('\nAvailable backups:');
  backupList.forEach((backup, index) => {
    console.log(`${index + 1}. ${backup}`);
  });

  // Get user input for backup selection
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  readline.question('\nEnter the number of the backup to restore: ', (answer) => {
    const backupIndex = parseInt(answer) - 1;
    if (backupIndex < 0 || backupIndex >= backupList.length) {
      console.error('Invalid backup selection');
      process.exit(1);
    }

    const selectedBackup = backupList[backupIndex];
    console.log(`\nRolling back to: ${selectedBackup}`);

    // Execute rollback commands on server
    const rollbackCommands = [
      `cd ${config.server.path}`,
      `tar -xzf backup/${selectedBackup}`,
      'npm install --production',
      'pm2 restart ecosystem.config.js'
    ].join(' && ');

    execSync(
      `ssh -p ${config.server.port} ${config.server.user}@${config.server.host} "${rollbackCommands}"`,
      { stdio: 'inherit' }
    );

    console.log('Rollback completed successfully!');
    readline.close();
  });
} catch (error) {
  console.error('Rollback failed:', error);
  process.exit(1);
} 